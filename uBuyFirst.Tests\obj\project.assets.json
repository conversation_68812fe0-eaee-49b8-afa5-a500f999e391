{"version": 3, "targets": {".NETFramework,Version=v4.7.2": {"DevExpress.Data/24.2.6": {"type": "package", "compile": {"lib/net462/DevExpress.Data.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net462/DevExpress.Data.v24.2.dll": {"related": ".xml"}}}, "Microsoft.ApplicationInsights/2.22.0": {"type": "package", "dependencies": {"System.Diagnostics.DiagnosticSource": "5.0.0"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net46/Microsoft.ApplicationInsights.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net46/Microsoft.ApplicationInsights.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Bcl.HashCode/1.1.1": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net461/Microsoft.Bcl.HashCode.dll": {}}, "runtime": {"lib/net461/Microsoft.Bcl.HashCode.dll": {"related": ".xml"}}}, "Microsoft.CodeCoverage/17.11.1": {"type": "package", "compile": {"lib/net462/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {}}, "runtime": {"lib/net462/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {}}, "build": {"build/netstandard2.0/Microsoft.CodeCoverage.props": {}, "build/netstandard2.0/Microsoft.CodeCoverage.targets": {}}}, "Microsoft.NET.Test.Sdk/17.11.1": {"type": "package", "dependencies": {"Microsoft.CodeCoverage": "17.11.1"}, "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}, "build": {"build/net462/Microsoft.NET.Test.Sdk.props": {}, "build/net462/Microsoft.NET.Test.Sdk.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.NET.Test.Sdk.props": {}}}, "Microsoft.Testing.Extensions.Telemetry/1.4.3": {"type": "package", "dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.Testing.Platform": "1.4.3"}, "compile": {"lib/netstandard2.0/Microsoft.Testing.Extensions.Telemetry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Testing.Extensions.Telemetry.dll": {"related": ".xml"}}, "resource": {"lib/netstandard2.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "zh-Han<PERSON>"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Testing.Extensions.Telemetry.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Testing.Extensions.Telemetry.props": {}}}, "Microsoft.Testing.Extensions.TrxReport.Abstractions/1.4.3": {"type": "package", "dependencies": {"Microsoft.Testing.Platform": "1.4.3"}, "compile": {"lib/netstandard2.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Testing.Extensions.VSTestBridge/1.4.3": {"type": "package", "dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.TestPlatform.ObjectModel": "17.11.1", "Microsoft.Testing.Extensions.Telemetry": "1.4.3", "Microsoft.Testing.Extensions.TrxReport.Abstractions": "1.4.3", "Microsoft.Testing.Platform": "1.4.3"}, "compile": {"lib/netstandard2.0/Microsoft.Testing.Extensions.VSTestBridge.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Testing.Extensions.VSTestBridge.dll": {"related": ".xml"}}, "resource": {"lib/netstandard2.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Testing.Platform/1.4.3": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Testing.Platform.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Testing.Platform.dll": {"related": ".xml"}}, "resource": {"lib/netstandard2.0/cs/Microsoft.Testing.Platform.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/Microsoft.Testing.Platform.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Microsoft.Testing.Platform.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/Microsoft.Testing.Platform.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/Microsoft.Testing.Platform.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/Microsoft.Testing.Platform.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/Microsoft.Testing.Platform.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/Microsoft.Testing.Platform.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/Microsoft.Testing.Platform.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/Microsoft.Testing.Platform.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/Microsoft.Testing.Platform.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/Microsoft.Testing.Platform.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/Microsoft.Testing.Platform.resources.dll": {"locale": "zh-Han<PERSON>"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Testing.Platform.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Testing.Platform.props": {}}}, "Microsoft.Testing.Platform.MSBuild/1.4.3": {"type": "package", "dependencies": {"Microsoft.Testing.Platform": "1.4.3"}, "compile": {"lib/netstandard2.0/Microsoft.Testing.Platform.MSBuild.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Testing.Platform.MSBuild.dll": {"related": ".xml"}}, "resource": {"lib/netstandard2.0/cs/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "zh-Han<PERSON>"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Testing.Platform.MSBuild.props": {}, "buildTransitive/netstandard2.0/Microsoft.Testing.Platform.MSBuild.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Testing.Platform.MSBuild.props": {}, "buildMultiTargeting/Microsoft.Testing.Platform.MSBuild.targets": {}}}, "Microsoft.TestPlatform.ObjectModel/17.11.1": {"type": "package", "dependencies": {"System.Reflection.Metadata": "1.6.0"}, "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Configuration", "System.Core", "System.Runtime", "System.Runtime.Serialization", "System.Xml", "mscorlib"], "compile": {"lib/net462/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/net462/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/net462/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}}, "runtime": {"lib/net462/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/net462/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/net462/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}}, "resource": {"lib/net462/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "cs"}, "lib/net462/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "cs"}, "lib/net462/de/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "de"}, "lib/net462/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "de"}, "lib/net462/es/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "es"}, "lib/net462/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "es"}, "lib/net462/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "fr"}, "lib/net462/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "fr"}, "lib/net462/it/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "it"}, "lib/net462/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "it"}, "lib/net462/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ja"}, "lib/net462/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ja"}, "lib/net462/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ko"}, "lib/net462/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ko"}, "lib/net462/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pl"}, "lib/net462/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pl"}, "lib/net462/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pt-BR"}, "lib/net462/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pt-BR"}, "lib/net462/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ru"}, "lib/net462/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ru"}, "lib/net462/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "tr"}, "lib/net462/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "tr"}, "lib/net462/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/net462/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Hans"}, "lib/net462/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/net462/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "MSTest.Analyzers/3.6.4": {"type": "package"}, "MSTest.TestAdapter/3.6.4": {"type": "package", "dependencies": {"Microsoft.Testing.Extensions.VSTestBridge": "1.4.3", "Microsoft.Testing.Platform.MSBuild": "1.4.3"}, "build": {"build/net462/MSTest.TestAdapter.props": {}, "build/net462/MSTest.TestAdapter.targets": {}}}, "MSTest.TestFramework/3.6.4": {"type": "package", "compile": {"lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll": {"related": ".xml"}, "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.dll": {"related": ".Extensions.xml;.xml"}}, "runtime": {"lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll": {"related": ".xml"}, "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.dll": {"related": ".Extensions.xml;.xml"}}, "resource": {"lib/net462/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "cs"}, "lib/net462/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "de"}, "lib/net462/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "es"}, "lib/net462/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "fr"}, "lib/net462/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "it"}, "lib/net462/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ja"}, "lib/net462/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ko"}, "lib/net462/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "pl"}, "lib/net462/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "pt-BR"}, "lib/net462/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ru"}, "lib/net462/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "tr"}, "lib/net462/zh-Hans/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "zh-Hans"}, "lib/net462/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Collections.Immutable/9.0.5": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Diagnostics.DiagnosticSource/5.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net46/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}}, "System.Formats.Nrbf/9.0.5": {"type": "package", "dependencies": {"Microsoft.Bcl.HashCode": "1.1.1", "System.Reflection.Metadata": "9.0.5", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net462/System.Formats.Nrbf.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Formats.Nrbf.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Reflection.Metadata/9.0.5": {"type": "package", "dependencies": {"System.Collections.Immutable": "9.0.5", "System.Memory": "4.5.5"}, "compile": {"lib/net462/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Reflection.Metadata.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Resources.Extensions/9.0.5": {"type": "package", "dependencies": {"System.Formats.Nrbf": "9.0.5", "System.Memory": "4.5.5"}, "compile": {"lib/net462/System.Resources.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Resources.Extensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/System.Resources.Extensions.targets": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}, "uBuyFirst/1.0.0": {"type": "project", "compile": {"bin/placeholder/uBuyFirst.dll": {}}, "runtime": {"bin/placeholder/uBuyFirst.dll": {}}}}}, "libraries": {"DevExpress.Data/24.2.6": {"sha512": "qjxEDTdE8fYjWK5ErBuYusKlnvwV8fFZuH/DIq+BQdNVeTJ0XwobYKjfDzYD9pgixbmPU7NsPr1aFFnMm9yMNg==", "type": "package", "path": "devexpress.data/24.2.6", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/DevExpress.Generator.dll", "devexpress.data.24.2.6.nupkg.sha512", "devexpress.data.nuspec", "lib/net462/DevExpress.Data.v24.2.dll", "lib/net462/DevExpress.Data.v24.2.xml", "lib/net8.0/DevExpress.Data.v24.2.dll", "lib/net8.0/DevExpress.Data.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "Microsoft.ApplicationInsights/2.22.0": {"sha512": "3AOM9bZtku7RQwHyMEY3tQMrHIgjcfRDa6YQpd/QG2LDGvMydSlL9Di+8LLMt7J2RDdfJ7/2jdYv6yHcMJAnNw==", "type": "package", "path": "microsoft.applicationinsights/2.22.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net452/Microsoft.ApplicationInsights.dll", "lib/net452/Microsoft.ApplicationInsights.pdb", "lib/net452/Microsoft.ApplicationInsights.xml", "lib/net46/Microsoft.ApplicationInsights.dll", "lib/net46/Microsoft.ApplicationInsights.pdb", "lib/net46/Microsoft.ApplicationInsights.xml", "lib/netstandard2.0/Microsoft.ApplicationInsights.dll", "lib/netstandard2.0/Microsoft.ApplicationInsights.pdb", "lib/netstandard2.0/Microsoft.ApplicationInsights.xml", "microsoft.applicationinsights.2.22.0.nupkg.sha512", "microsoft.applicationinsights.nuspec"]}, "Microsoft.Bcl.HashCode/1.1.1": {"sha512": "MalY0Y/uM/LjXtHfX/26l2VtN4LDNZ2OE3aumNOHDLsT4fNYy2hiHXI4CXCqKpNUNm7iJ2brrc4J89UdaL56FA==", "type": "package", "path": "microsoft.bcl.hashcode/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.HashCode.dll", "lib/net461/Microsoft.Bcl.HashCode.xml", "lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll", "lib/netcoreapp2.1/Microsoft.Bcl.HashCode.xml", "lib/netstandard2.0/Microsoft.Bcl.HashCode.dll", "lib/netstandard2.0/Microsoft.Bcl.HashCode.xml", "lib/netstandard2.1/Microsoft.Bcl.HashCode.dll", "lib/netstandard2.1/Microsoft.Bcl.HashCode.xml", "microsoft.bcl.hashcode.1.1.1.nupkg.sha512", "microsoft.bcl.hashcode.nuspec", "ref/net461/Microsoft.Bcl.HashCode.dll", "ref/netcoreapp2.1/Microsoft.Bcl.HashCode.dll", "ref/netstandard2.0/Microsoft.Bcl.HashCode.dll", "ref/netstandard2.1/Microsoft.Bcl.HashCode.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.CodeCoverage/17.11.1": {"sha512": "nPJqrcA5iX+Y0kqoT3a+pD/8lrW/V7ayqnEJQsTonSoPz59J8bmoQhcSN4G8+UJ64Hkuf0zuxnfuj2lkHOq4cA==", "type": "package", "path": "microsoft.codecoverage/17.11.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.txt", "build/netstandard2.0/CodeCoverage/CodeCoverage.config", "build/netstandard2.0/CodeCoverage/CodeCoverage.exe", "build/netstandard2.0/CodeCoverage/VanguardInstrumentationProfiler_x86.config", "build/netstandard2.0/CodeCoverage/amd64/CodeCoverage.exe", "build/netstandard2.0/CodeCoverage/amd64/VanguardInstrumentationProfiler_x64.config", "build/netstandard2.0/CodeCoverage/amd64/covrun64.dll", "build/netstandard2.0/CodeCoverage/amd64/msdia140.dll", "build/netstandard2.0/CodeCoverage/arm64/VanguardInstrumentationProfiler_arm64.config", "build/netstandard2.0/CodeCoverage/arm64/covrunarm64.dll", "build/netstandard2.0/CodeCoverage/arm64/msdia140.dll", "build/netstandard2.0/CodeCoverage/codecoveragemessages.dll", "build/netstandard2.0/CodeCoverage/coreclr/Microsoft.VisualStudio.CodeCoverage.Shim.dll", "build/netstandard2.0/CodeCoverage/covrun32.dll", "build/netstandard2.0/CodeCoverage/msdia140.dll", "build/netstandard2.0/InstrumentationEngine/alpine/x64/VanguardInstrumentationProfiler_x64.config", "build/netstandard2.0/InstrumentationEngine/alpine/x64/libCoverageInstrumentationMethod.so", "build/netstandard2.0/InstrumentationEngine/alpine/x64/libInstrumentationEngine.so", "build/netstandard2.0/InstrumentationEngine/arm64/MicrosoftInstrumentationEngine_arm64.dll", "build/netstandard2.0/InstrumentationEngine/macos/x64/VanguardInstrumentationProfiler_x64.config", "build/netstandard2.0/InstrumentationEngine/macos/x64/libCoverageInstrumentationMethod.dylib", "build/netstandard2.0/InstrumentationEngine/macos/x64/libInstrumentationEngine.dylib", "build/netstandard2.0/InstrumentationEngine/ubuntu/x64/VanguardInstrumentationProfiler_x64.config", "build/netstandard2.0/InstrumentationEngine/ubuntu/x64/libCoverageInstrumentationMethod.so", "build/netstandard2.0/InstrumentationEngine/ubuntu/x64/libInstrumentationEngine.so", "build/netstandard2.0/InstrumentationEngine/x64/MicrosoftInstrumentationEngine_x64.dll", "build/netstandard2.0/InstrumentationEngine/x86/MicrosoftInstrumentationEngine_x86.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Core.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Instrumentation.Core.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Instrumentation.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Interprocess.dll", "build/netstandard2.0/Microsoft.CodeCoverage.props", "build/netstandard2.0/Microsoft.CodeCoverage.targets", "build/netstandard2.0/Microsoft.DiaSymReader.dll", "build/netstandard2.0/Microsoft.VisualStudio.TraceDataCollector.dll", "build/netstandard2.0/Mono.Cecil.Pdb.dll", "build/netstandard2.0/Mono.Cecil.Rocks.dll", "build/netstandard2.0/Mono.Cecil.dll", "build/netstandard2.0/ThirdPartyNotices.txt", "build/netstandard2.0/cs/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/de/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/es/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/fr/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/it/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/ja/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/ko/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/pl/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/pt-BR/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/ru/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/tr/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "lib/net462/Microsoft.VisualStudio.CodeCoverage.Shim.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll", "microsoft.codecoverage.17.11.1.nupkg.sha512", "microsoft.codecoverage.nuspec"]}, "Microsoft.NET.Test.Sdk/17.11.1": {"sha512": "U3Ty4BaGoEu+T2bwSko9tWqWUOU16WzSFkq6U8zve75oRBMSLTBdMAZrVNNz1Tq12aCdDom9fcOcM9QZaFHqFg==", "type": "package", "path": "microsoft.net.test.sdk/17.11.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/net462/Microsoft.NET.Test.Sdk.props", "build/net462/Microsoft.NET.Test.Sdk.targets", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.Program.cs", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.Program.fs", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.Program.vb", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.props", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.targets", "buildMultiTargeting/Microsoft.NET.Test.Sdk.props", "lib/net462/_._", "lib/netcoreapp3.1/_._", "microsoft.net.test.sdk.17.11.1.nupkg.sha512", "microsoft.net.test.sdk.nuspec"]}, "Microsoft.Testing.Extensions.Telemetry/1.4.3": {"sha512": "dh8jnqWikxQXJ4kWy8B82PtSAlQCnvDKh1128arDmSW5OU5xWA84HwruV3TanXi3ZjIHn1wWFCgtMOhcDNwBow==", "type": "package", "path": "microsoft.testing.extensions.telemetry/1.4.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "build/net6.0/Microsoft.Testing.Extensions.Telemetry.props", "build/net7.0/Microsoft.Testing.Extensions.Telemetry.props", "build/net8.0/Microsoft.Testing.Extensions.Telemetry.props", "build/netstandard2.0/Microsoft.Testing.Extensions.Telemetry.props", "buildMultiTargeting/Microsoft.Testing.Extensions.Telemetry.props", "buildTransitive/net6.0/Microsoft.Testing.Extensions.Telemetry.props", "buildTransitive/net7.0/Microsoft.Testing.Extensions.Telemetry.props", "buildTransitive/net8.0/Microsoft.Testing.Extensions.Telemetry.props", "buildTransitive/netstandard2.0/Microsoft.Testing.Extensions.Telemetry.props", "lib/net6.0/Microsoft.Testing.Extensions.Telemetry.dll", "lib/net6.0/Microsoft.Testing.Extensions.Telemetry.xml", "lib/net6.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/zh-Hans/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/Microsoft.Testing.Extensions.Telemetry.dll", "lib/net7.0/Microsoft.Testing.Extensions.Telemetry.xml", "lib/net7.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/zh-Hans/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/Microsoft.Testing.Extensions.Telemetry.dll", "lib/net8.0/Microsoft.Testing.Extensions.Telemetry.xml", "lib/net8.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/Microsoft.Testing.Extensions.Telemetry.dll", "lib/netstandard2.0/Microsoft.Testing.Extensions.Telemetry.xml", "lib/netstandard2.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll", "microsoft.testing.extensions.telemetry.1.4.3.nupkg.sha512", "microsoft.testing.extensions.telemetry.nuspec"]}, "Microsoft.Testing.Extensions.TrxReport.Abstractions/1.4.3": {"sha512": "16sWznD6ZMok/zgW+vrO6zerCFMD9N+ey9bi1iV/e9xxsQb4V4y/aW6cY/Y7E9jA7pc+aZ6ffZby43yxQOoYZA==", "type": "package", "path": "microsoft.testing.extensions.trxreport.abstractions/1.4.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net6.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll", "lib/net6.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.xml", "lib/net7.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll", "lib/net7.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.xml", "lib/net8.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll", "lib/net8.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.xml", "lib/netstandard2.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll", "lib/netstandard2.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.xml", "microsoft.testing.extensions.trxreport.abstractions.1.4.3.nupkg.sha512", "microsoft.testing.extensions.trxreport.abstractions.nuspec"]}, "Microsoft.Testing.Extensions.VSTestBridge/1.4.3": {"sha512": "xZ6oyNYh2aM5Wb+HJAy1fj2C4CNRVhINXHCjlWs/2C8hEIpdqVSpP3y6HWUN40KpFqyGD4myHGR1Rflm28UpcQ==", "type": "package", "path": "microsoft.testing.extensions.vstestbridge/1.4.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net6.0/Microsoft.Testing.Extensions.VSTestBridge.dll", "lib/net6.0/Microsoft.Testing.Extensions.VSTestBridge.xml", "lib/net6.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/zh-Hans/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/Microsoft.Testing.Extensions.VSTestBridge.dll", "lib/net7.0/Microsoft.Testing.Extensions.VSTestBridge.xml", "lib/net7.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/zh-Hans/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/Microsoft.Testing.Extensions.VSTestBridge.dll", "lib/net8.0/Microsoft.Testing.Extensions.VSTestBridge.xml", "lib/net8.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/Microsoft.Testing.Extensions.VSTestBridge.dll", "lib/netstandard2.0/Microsoft.Testing.Extensions.VSTestBridge.xml", "lib/netstandard2.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "microsoft.testing.extensions.vstestbridge.1.4.3.nupkg.sha512", "microsoft.testing.extensions.vstestbridge.nuspec"]}, "Microsoft.Testing.Platform/1.4.3": {"sha512": "NedIbwl1T7+ZMeg7gwk0Db8/RFLf0siyVpeTcRMMOle6Xl/ujaYOM4Aduo8rEfVqNj3kcQ7blegpyT3dHi+0PA==", "type": "package", "path": "microsoft.testing.platform/1.4.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "build/net6.0/Microsoft.Testing.Platform.props", "build/net7.0/Microsoft.Testing.Platform.props", "build/net8.0/Microsoft.Testing.Platform.props", "build/netstandard2.0/Microsoft.Testing.Platform.props", "buildMultiTargeting/Microsoft.Testing.Platform.props", "buildTransitive/net6.0/Microsoft.Testing.Platform.props", "buildTransitive/net7.0/Microsoft.Testing.Platform.props", "buildTransitive/net8.0/Microsoft.Testing.Platform.props", "buildTransitive/netstandard2.0/Microsoft.Testing.Platform.props", "lib/net6.0/Microsoft.Testing.Platform.dll", "lib/net6.0/Microsoft.Testing.Platform.xml", "lib/net6.0/cs/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/de/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/es/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/fr/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/it/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/ja/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/ko/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/pl/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/pt-BR/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/ru/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/tr/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/zh-Hans/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/zh-Hant/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/Microsoft.Testing.Platform.dll", "lib/net7.0/Microsoft.Testing.Platform.xml", "lib/net7.0/cs/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/de/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/es/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/fr/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/it/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/ja/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/ko/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/pl/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/pt-BR/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/ru/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/tr/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/zh-Hans/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/zh-Hant/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/Microsoft.Testing.Platform.dll", "lib/net8.0/Microsoft.Testing.Platform.xml", "lib/net8.0/cs/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/de/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/es/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/fr/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/it/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/ja/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/ko/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/pl/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/pt-BR/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/ru/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/tr/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/Microsoft.Testing.Platform.dll", "lib/netstandard2.0/Microsoft.Testing.Platform.xml", "lib/netstandard2.0/cs/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/de/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/es/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/fr/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/it/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/ja/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/ko/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/pl/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/ru/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/tr/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/zh-Hans/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Testing.Platform.resources.dll", "microsoft.testing.platform.1.4.3.nupkg.sha512", "microsoft.testing.platform.nuspec"]}, "Microsoft.Testing.Platform.MSBuild/1.4.3": {"sha512": "1gGqgHtiZ6tZn/6Tby+qlKpNe5Ye/5LnxlSsyl4XMZ4m4V+Cu1K1m+gD1zxoxHIvLjgX8mCnQRK95MGBBFuumw==", "type": "package", "path": "microsoft.testing.platform.msbuild/1.4.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "_MSBuildTasks/netstandard2.0/Microsoft.Testing.Platform.MSBuild.dll", "_MSBuildTasks/netstandard2.0/Microsoft.Testing.Platform.dll", "build/net6.0/Microsoft.Testing.Platform.MSBuild.props", "build/net6.0/Microsoft.Testing.Platform.MSBuild.targets", "build/net7.0/Microsoft.Testing.Platform.MSBuild.props", "build/net7.0/Microsoft.Testing.Platform.MSBuild.targets", "build/net8.0/Microsoft.Testing.Platform.MSBuild.props", "build/net8.0/Microsoft.Testing.Platform.MSBuild.targets", "build/netstandard2.0/Microsoft.Testing.Platform.MSBuild.props", "build/netstandard2.0/Microsoft.Testing.Platform.MSBuild.targets", "buildMultiTargeting/Microsoft.Testing.Platform.MSBuild.VSTest.targets", "buildMultiTargeting/Microsoft.Testing.Platform.MSBuild.props", "buildMultiTargeting/Microsoft.Testing.Platform.MSBuild.targets", "buildTransitive/net6.0/Microsoft.Testing.Platform.MSBuild.props", "buildTransitive/net6.0/Microsoft.Testing.Platform.MSBuild.targets", "buildTransitive/net7.0/Microsoft.Testing.Platform.MSBuild.props", "buildTransitive/net7.0/Microsoft.Testing.Platform.MSBuild.targets", "buildTransitive/net8.0/Microsoft.Testing.Platform.MSBuild.props", "buildTransitive/net8.0/Microsoft.Testing.Platform.MSBuild.targets", "buildTransitive/netstandard2.0/Microsoft.Testing.Platform.MSBuild.props", "buildTransitive/netstandard2.0/Microsoft.Testing.Platform.MSBuild.targets", "lib/net6.0/Microsoft.Testing.Platform.MSBuild.dll", "lib/net6.0/Microsoft.Testing.Platform.MSBuild.xml", "lib/net6.0/cs/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/de/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/es/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/fr/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/it/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/ja/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/ko/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/pl/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/pt-BR/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/ru/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/tr/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/zh-Hans/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/zh-Hant/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/Microsoft.Testing.Platform.MSBuild.dll", "lib/net7.0/Microsoft.Testing.Platform.MSBuild.xml", "lib/net7.0/cs/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/de/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/es/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/fr/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/it/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/ja/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/ko/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/pl/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/pt-BR/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/ru/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/tr/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/zh-Hans/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/zh-Hant/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/Microsoft.Testing.Platform.MSBuild.dll", "lib/net8.0/Microsoft.Testing.Platform.MSBuild.xml", "lib/net8.0/cs/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/de/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/es/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/fr/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/it/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/ja/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/ko/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/pl/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/pt-BR/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/ru/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/tr/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/Microsoft.Testing.Platform.MSBuild.dll", "lib/netstandard2.0/Microsoft.Testing.Platform.MSBuild.xml", "lib/netstandard2.0/cs/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/de/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/es/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/fr/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/it/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/ja/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/ko/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/pl/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/ru/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/tr/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Testing.Platform.MSBuild.resources.dll", "microsoft.testing.platform.msbuild.1.4.3.nupkg.sha512", "microsoft.testing.platform.msbuild.nuspec"]}, "Microsoft.TestPlatform.ObjectModel/17.11.1": {"sha512": "E2jZqAU6JeWEVsyOEOrSW1o1bpHLgb25ypvKNB/moBXPVsFYBPd/Jwi7OrYahG50J83LfHzezYI+GaEkpAotiA==", "type": "package", "path": "microsoft.testplatform.objectmodel/17.11.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net462/Microsoft.TestPlatform.CoreUtilities.dll", "lib/net462/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/net462/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/net462/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/Microsoft.TestPlatform.CoreUtilities.dll", "lib/netstandard2.0/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/netstandard2.0/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "microsoft.testplatform.objectmodel.17.11.1.nupkg.sha512", "microsoft.testplatform.objectmodel.nuspec"]}, "MSTest.Analyzers/3.6.4": {"sha512": "4gU/VdItLebmE2+UkOaqffVmVa/in0VeIF9fmN/fG0tj5AHAasjasJcZa9U2uXBNX03cKCWlgWenlhKLz343NQ==", "type": "package", "path": "mstest.analyzers/3.6.4", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "analyzers/dotnet/cs/MSTest.Analyzers.CodeFixes.dll", "analyzers/dotnet/cs/MSTest.Analyzers.dll", "mstest.analyzers.3.6.4.nupkg.sha512", "mstest.analyzers.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "MSTest.TestAdapter/3.6.4": {"sha512": "YdwseRA+nDhRqD2oPHjCE4KzLEN5B10A61lOslE3N3OvUwHJ6ezyZZjYWf7mrZ8jckCcx/UlBclTzgWUpMpPQw==", "type": "package", "path": "mstest.testadapter/3.6.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "build/_localization/cs/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/cs/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/de/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/de/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/es/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/es/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/fr/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/fr/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/it/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/it/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/ja/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/ja/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/ko/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/ko/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/pl/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/pl/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/pt-BR/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/pt-BR/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/ru/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/ru/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/tr/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/tr/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/zh-Hant/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/zh-Hant/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/net462/MSTest.TestAdapter.props", "build/net462/MSTest.TestAdapter.targets", "build/net462/Microsoft.TestPlatform.AdapterUtilities.dll", "build/net462/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/net462/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/net6.0/MSTest.TestAdapter.props", "build/net6.0/MSTest.TestAdapter.targets", "build/net6.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/net6.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/net6.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/net6.0/winui/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/net6.0/winui/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net7.0/MSTest.TestAdapter.props", "build/net7.0/MSTest.TestAdapter.targets", "build/net7.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/net7.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/net7.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net7.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/net8.0/MSTest.TestAdapter.props", "build/net8.0/MSTest.TestAdapter.targets", "build/net8.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/net8.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/net8.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net8.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/netcoreapp3.1/MSTest.TestAdapter.props", "build/netcoreapp3.1/MSTest.TestAdapter.targets", "build/netcoreapp3.1/Microsoft.TestPlatform.AdapterUtilities.dll", "build/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/netstandard2.0/MSTest.TestAdapter.props", "build/netstandard2.0/MSTest.TestAdapter.targets", "build/netstandard2.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/netstandard2.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/netstandard2.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/uap10.0/MSTest.TestAdapter.props", "build/uap10.0/MSTest.TestAdapter.targets", "build/uap10.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/uap10.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/uap10.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "mstest.testadapter.3.6.4.nupkg.sha512", "mstest.testadapter.nuspec"]}, "MSTest.TestFramework/3.6.4": {"sha512": "3nV+2CJluKmiJpCSqQfXu5idCq35+vqFywjScyauTIz0Zk7KJw7Qpzv8gtwow0To7pxIlIvwkq9rbMB+V6eOow==", "type": "package", "path": "mstest.testframework/3.6.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "build/net6.0/MSTest.TestFramework.targets", "build/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "build/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net6.0/winui/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "build/net6.0/winui/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net7.0/MSTest.TestFramework.targets", "build/net7.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "build/net7.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net8.0/MSTest.TestFramework.targets", "build/net8.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "build/net8.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net462/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net6.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net7.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net7.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net8.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net8.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard2.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "mstest.testframework.3.6.4.nupkg.sha512", "mstest.testframework.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections.Immutable/9.0.5": {"sha512": "lVPwqkFWucEB13zcXpvMCmG+xp0HTSxVyAiG7c0AC8qsQ1ZaC5O8evb9QGR6PKmJZch3VuFKnzkl+SA78dvjeA==", "type": "package", "path": "system.collections.immutable/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Collections.Immutable.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Collections.Immutable.targets", "lib/net462/System.Collections.Immutable.dll", "lib/net462/System.Collections.Immutable.xml", "lib/net8.0/System.Collections.Immutable.dll", "lib/net8.0/System.Collections.Immutable.xml", "lib/net9.0/System.Collections.Immutable.dll", "lib/net9.0/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "system.collections.immutable.9.0.5.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.DiagnosticSource/5.0.0": {"sha512": "tCQTzPsGZh/A9LhhA6zrqCRV4hOHsK90/G7q3Khxmn6tnB1PuNU0cRaKANP2AWcF9bn0zsuOoZOSrHuJk6oNBA==", "type": "package", "path": "system.diagnostics.diagnosticsource/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/System.Diagnostics.DiagnosticSource.dll", "lib/net45/System.Diagnostics.DiagnosticSource.xml", "lib/net46/System.Diagnostics.DiagnosticSource.dll", "lib/net46/System.Diagnostics.DiagnosticSource.xml", "lib/net5.0/System.Diagnostics.DiagnosticSource.dll", "lib/net5.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.xml", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.dll", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.5.0.0.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Formats.Nrbf/9.0.5": {"sha512": "ywLf8kpOu21wyRpobN/gNWjNmL7oSPFvs5UoTXGxGpE7tM7YlbBdC8Y0PPd7QYK7/EQK/dzZRF09+RorOul+6A==", "type": "package", "path": "system.formats.nrbf/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Formats.Nrbf.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Formats.Nrbf.targets", "lib/net462/System.Formats.Nrbf.dll", "lib/net462/System.Formats.Nrbf.xml", "lib/net8.0/System.Formats.Nrbf.dll", "lib/net8.0/System.Formats.Nrbf.xml", "lib/net9.0/System.Formats.Nrbf.dll", "lib/net9.0/System.Formats.Nrbf.xml", "lib/netstandard2.0/System.Formats.Nrbf.dll", "lib/netstandard2.0/System.Formats.Nrbf.xml", "system.formats.nrbf.9.0.5.nupkg.sha512", "system.formats.nrbf.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Metadata/9.0.5": {"sha512": "ulMSHTq7/6g8ZWFFPSQreZqe7FIrPXOyq7rXUsmfQX3d/sK2wqEBxxFeWeuQBxrVXEj+w+UremzgcjWdJaIh1A==", "type": "package", "path": "system.reflection.metadata/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Reflection.Metadata.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Reflection.Metadata.targets", "lib/net462/System.Reflection.Metadata.dll", "lib/net462/System.Reflection.Metadata.xml", "lib/net8.0/System.Reflection.Metadata.dll", "lib/net8.0/System.Reflection.Metadata.xml", "lib/net9.0/System.Reflection.Metadata.dll", "lib/net9.0/System.Reflection.Metadata.xml", "lib/netstandard2.0/System.Reflection.Metadata.dll", "lib/netstandard2.0/System.Reflection.Metadata.xml", "system.reflection.metadata.9.0.5.nupkg.sha512", "system.reflection.metadata.nuspec", "useSharedDesignerContext.txt"]}, "System.Resources.Extensions/9.0.5": {"sha512": "FqPoXnG0yyl7Q9PS6WoC0xOz5bQapsC+frqmyzuzGJZKMWWH9L/fgUPkLcwiSx6ow3aIPYREtAutLMuk5NqZvw==", "type": "package", "path": "system.resources.extensions/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Resources.Extensions.targets", "buildTransitive/net462/System.Resources.Extensions.targets", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Resources.Extensions.targets", "lib/net462/System.Resources.Extensions.dll", "lib/net462/System.Resources.Extensions.xml", "lib/net8.0/System.Resources.Extensions.dll", "lib/net8.0/System.Resources.Extensions.xml", "lib/net9.0/System.Resources.Extensions.dll", "lib/net9.0/System.Resources.Extensions.xml", "lib/netstandard2.0/System.Resources.Extensions.dll", "lib/netstandard2.0/System.Resources.Extensions.xml", "system.resources.extensions.9.0.5.nupkg.sha512", "system.resources.extensions.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.ValueTuple/4.5.0": {"sha512": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "type": "package", "path": "system.valuetuple/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.ValueTuple.dll", "lib/net461/System.ValueTuple.xml", "lib/net47/System.ValueTuple.dll", "lib/net47/System.ValueTuple.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.ValueTuple.dll", "lib/netstandard1.0/System.ValueTuple.xml", "lib/netstandard2.0/_._", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.ValueTuple.dll", "ref/net47/System.ValueTuple.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/_._", "ref/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.valuetuple.4.5.0.nupkg.sha512", "system.valuetuple.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "uBuyFirst/1.0.0": {"type": "project", "path": "../EbaySniper/uBuyFirst.csproj", "msbuildProject": "../EbaySniper/uBuyFirst.csproj"}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.7.2": ["DevExpress.Data >= 24.2.6", "MSTest.Analyzers >= 3.6.4", "MSTest.TestAdapter >= 3.6.4", "MSTest.TestFramework >= 3.6.4", "Microsoft.NET.Test.Sdk >= 17.11.1", "System.Resources.Extensions >= 9.0.5", "uBuyFirst >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\ZOCS\\Visual Studio 2010\\Projects\\EbaySniper\\uBuyFirst.Tests\\uBuyFirst.Tests.csproj", "projectName": "uBuyFirst.Tests", "projectPath": "C:\\ZOCS\\Visual Studio 2010\\Projects\\EbaySniper\\uBuyFirst.Tests\\uBuyFirst.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\ZOCS\\Visual Studio 2010\\Projects\\EbaySniper\\uBuyFirst.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"targetAlias": "net472", "projectReferences": {"C:\\ZOCS\\Visual Studio 2010\\Projects\\EbaySniper\\EbaySniper\\uBuyFirst.csproj": {"projectPath": "C:\\ZOCS\\Visual Studio 2010\\Projects\\EbaySniper\\EbaySniper\\uBuyFirst.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net472": {"targetAlias": "net472", "dependencies": {"DevExpress.Data": {"target": "Package", "version": "[24.2.6, )"}, "MSTest.Analyzers": {"target": "Package", "version": "[3.6.4, )", "versionOverride": "[3.6.4, )"}, "MSTest.TestAdapter": {"target": "Package", "version": "[3.6.4, )", "versionOverride": "[3.6.4, )"}, "MSTest.TestFramework": {"target": "Package", "version": "[3.6.4, )", "versionOverride": "[3.6.4, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.11.1, )", "versionOverride": "[17.11.1, )"}, "System.Resources.Extensions": {"target": "Package", "version": "[9.0.5, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}