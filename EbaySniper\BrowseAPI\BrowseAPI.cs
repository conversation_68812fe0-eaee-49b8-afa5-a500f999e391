//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace BrowseAPI
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using Newtonsoft.Json;
    using Fonlow.Net.Http;


    /// <summary>
    /// The type that defines the array of product identifiers associated with the item. This container is returned if the seller has associated the eBay Product Identifier (ePID) with the item, and in the request <code>fieldgroups</code> is set to <code>PRODUCT</code>.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class AdditionalProductIdentity
    {

        /// <summary>
        /// An array of product identifier/value pairs for the product associated with the item. This is returned if the seller has associated the eBay Product Identifier (ePID) with the item and the request has <code>fieldgroups</code> set to <code>PRODUCT</code>.<br><br>The following table shows what is returned, based on the item information provided by the seller, when <code>fieldgroups</code> is set to <code>PRODUCT</code>.<br><br><div style="overflow-x:auto;"><table border=1><tr><th>ePID Provided</th><th>Product ID(s) Provided</th><th>Response</th></tr><tr><td>No</td><td>No</td><td>The <code>AdditionalProductIdentity</code> container is <i>not</i> returned.</td></tr><tr><td>No</td><td>Yes</td><td>The <code>AdditionalProductIdentity</code> container is <i>not</i> returned but the product identifiers specified by the seller are returned in the <code>localizedAspects</code> container.</td></tr><tr><td>Yes</td><td>No</td><td>The <code>AdditionalProductIdentity</code> container is returned listing the product identifiers of the product.</td></tr><tr><td>Yes</td><td>Yes</td><td>The <code>AdditionalProductIdentity</code> container is returned listing all the product identifiers of the product and the product identifiers specified by the seller are returned in the <code>localizedAspects</code> container.</td></tr></table></div>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "productIdentity")]
        public ProductIdentity[] ProductIdentity { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the product identifier type/value pairs of product associated with an item.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ProductIdentity
    {

        /// <summary>
        /// The type of product identifier, such as UPC and EAN.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "identifierType")]
        public string IdentifierType { get; set; }

        /// <summary>
        /// The product identifier value.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "identifierValue")]
        public string IdentifierValue { get; set; }
    }

    /// <summary>
    /// This container describes an add-on service that may be selected for an item or that may apply automatically. A charge may be associated with the add-on service.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class AddonService
    {

        /// <summary>
        /// This field indicates whether the add-on service must be selected for the item. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:AddonServiceSelectionEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "selection")]
        public string Selection { get; set; }

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "serviceFee")]
        public ConvertedAmount ServiceFee { get; set; }

        /// <summary>
        /// The ID number of the add-on service.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "serviceId")]
        public string ServiceId { get; set; }

        /// <summary>
        /// The type of add-on service, such as <code>AUTHENTICITY_GUARANTEE</code>. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:AddonServiceTypeEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "serviceType")]
        public string ServiceType { get; set; }
    }

    /// <summary>
    /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ConvertedAmount
    {

        /// <summary>
        /// The three-letter <a href="https://www.iso.org/iso-4217-currency-codes.html " target="_blank">ISO 4217</a> code representing the currency of the amount in the <code>convertedFromValue</code> field. This value is required or returned only if currency conversion/localization is required, and represents the pre-conversion currency. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/ba:CurrencyCodeEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "convertedFromCurrency")]
        public string ConvertedFromCurrency { get; set; }

        /// <summary>
        /// The monetary amount before any conversion is performed, in the currency specified by the <code>convertedFromCurrency</code> field. This value is required or returned only if currency conversion/localization is required. The <code>value</code> field contains the converted amount of this value, in the currency specified by the <code>currency</code> field.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "convertedFromValue")]
        public string ConvertedFromValue { get; set; }

        /// <summary>
        /// The three-letter <a href="https://www.iso.org/iso-4217-currency-codes.html " target="_blank">ISO 4217</a> code representing the currency of the amount in the <code>value</code> field. If currency conversion/localization is required, this is the post-conversion currency of the amount in the <code>value</code> field.<br><br><b>Default:</b> The currency of the authenticated user's country. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/ba:CurrencyCodeEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "currency")]
        public string Currency { get; set; }

        /// <summary>
        /// The monetary amount in the currency specified by the <code>currency</code> field. If currency conversion/localization is required, this value is the converted amount, and the <code>convertedFromValue</code> field contains the amount in the original currency.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "value")]
        public string Value { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for an address.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class Address
    {

        /// <summary>
        /// The first line of the street address.<br><br><span class="tablenote"><b>Note:</b> This is conditionally returned in the <code>itemLocation</code> field.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "addressLine1")]
        public string AddressLine1 { get; set; }

        /// <summary>
        /// The second line of the street address. This field is not always used, but can be used for "Suite Number" or "Apt Number".
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "addressLine2")]
        public string AddressLine2 { get; set; }

        /// <summary>
        /// The city of the address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "city")]
        public string City { get; set; }

        /// <summary>
        /// The two-letter <a href="https://www.iso.org/iso-3166-country-codes.html " target="_blank">ISO 3166</a> standard code for the country of the address. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/ba:CountryCodeEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "country")]
        public string Country { get; set; }

        /// <summary>
        /// The county of the address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "county")]
        public string County { get; set; }

        /// <summary>
        /// The postal code (or zip code in US) code of the address. Sellers set a postal code (or zip code in US) for items when they are listed. The postal code is used for calculating proximity searches. It is anonymized when returned in <code>itemLocation.postalCode</code> via the API.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "postalCode")]
        public string PostalCode { get; set; }

        /// <summary>
        /// The state or province of the address.<br><br><span class="tablenote"><b>Note:</b> This is conditionally returned in the <code>itemLocation</code> field.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "stateOrProvince")]
        public string StateOrProvince { get; set; }
    }

    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class Amount
    {

        /// <summary>
        /// The list of valid currencies. Each <a href="https://www.iso.org/iso-4217-currency-codes.html " target="_blank">ISO 4217</a> currency code includes the currency name followed by the numeric value.<br><br>For example, the Canadian Dollar code (CAD) would take the following form: <code>Canadian Dollar, 124</code>. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/ba:CurrencyCodeEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "currency")]
        public string Currency { get; set; }

        /// <summary>
        /// The value of the discounted amount.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "value")]
        public string Value { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the name/value pairs for the aspects of the product. For example: BRAND/Apple
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class BrowseAspect
    {

        /// <summary>
        /// The text representing the name of the aspect for the name/value pair, such as Brand.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "localizedName")]
        public string LocalizedName { get; set; }

        /// <summary>
        /// The text representing the value of the aspect for the name/value pair, such as Apple.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "localizedValues")]
        public string[] LocalizedValues { get; set; }
    }

    /// <summary>
    /// The type that define the fields for the aspect information. Aspects are the variations of an item, such as color, size, etc.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class AspectDistribution
    {

        /// <summary>
        /// An array of containers for the various values of the aspect and the match count, and a HATEOAS reference (<code>refinementHref</code>) for this aspect.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "aspectValueDistributions")]
        public AspectValueDistribution[] AspectValueDistributions { get; set; }

        /// <summary>
        /// The name of an aspect, such as <i>Brand</i>, <i>Color</i>, etc.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "localizedAspectName")]
        public string LocalizedAspectName { get; set; }
    }

    /// <summary>
    /// The container that defines the fields for the conditions refinements. This container is returned when <code>fieldgroups</code> is set to <code>ASPECT_REFINEMENTS</code> or <code>FULL</code> in the request.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class AspectValueDistribution
    {

        /// <summary>
        /// The value of an aspect. For example, Red is a value for the aspect Color.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "localizedAspectValue")]
        public string LocalizedAspectValue { get; set; }

        /// <summary>
        /// The number of items with this aspect.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "matchCount")]
        public System.Nullable<System.Int32> MatchCount { get; set; }

        /// <summary>
        /// A HATEOAS reference for this aspect.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "refinementHref")]
        public string RefinementHref { get; set; }
    }

    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class AspectGroup
    {

        /// <summary>
        /// An array of the name/value pairs for the aspects of the product. For example: BRAND/Apple
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "aspects")]
        public BrowseAspect[] Aspects { get; set; }

        /// <summary>
        /// The name of a group of aspects.<br><br>In the following example, <b>Product Identifiers</b> and <b>Process</b> are product aspect group names. Under the group name are the product aspect name/value pairs.<br><br><b>Product Identifiers</b><br>&nbsp;&nbsp;&nbsp;Brand/Apple<br>&nbsp;&nbsp;&nbsp;Product Family/iMac<br><br><b>Processor</b><br>&nbsp;&nbsp;&nbsp;Processor Type/Intel<br>&nbsp;&nbsp;&nbsp;Processor Speed/3.10
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "localizedGroupName")]
        public string LocalizedGroupName { get; set; }
    }

    /// <summary>
    /// The type the defines attribute name/value pair fields that specify a product. The type of data depends on the context. For example, if you were using this to specify a specific vehicle, the attribute names would be <i>Make</i>, <i>Model</i>, <i>Year</i>, etc.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class AttributeNameValue
    {

        /// <summary>
        /// The name of the product attribute, such as <i>Make</i>, <i>Model</i>, <i>Year</i>, etc.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "name")]
        public string Name { get; set; }

        /// <summary>
        /// The value for the <code>name</code> attribute, such as <i>BMW</i>, <i>R1200GS</i>, <i>2011</i>, etc.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "value")]
        public string Value { get; set; }
    }

    /// <summary>
    /// A type that identifies whether the item is qualified for the Authenticity Guarantee program.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class AuthenticityGuaranteeProgram
    {

        /// <summary>
        /// An indication that the item is qualified for the Authenticity Guarantee program.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "description")]
        public string Description { get; set; }

        /// <summary>
        /// The URL to the Authenticity Guarantee program terms of use.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "termsWebUrl")]
        public string TermsWebUrl { get; set; }
    }

    /// <summary>
    /// A type that identifies whether the item is from a verified seller.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class AuthenticityVerificationProgram
    {

        /// <summary>
        /// An indication that the item is from a verified seller.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "description")]
        public string Description { get; set; }

        /// <summary>
        /// The URL to the Authenticity Verification program terms of use.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "termsWebUrl")]
        public string TermsWebUrl { get; set; }
    }

    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class AutoCorrections
    {

        /// <summary>
        /// The automatically spell-corrected keyword from the request.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "q")]
        public string Q { get; set; }
    }

    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class AvailableCoupon
    {

        /// <summary>
        /// This type is used to provide the expiration date of a coded coupon.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "constraint")]
        public CouponConstraint Constraint { get; set; }

        [System.Runtime.Serialization.DataMember(Name = "discountAmount")]
        public Amount DiscountAmount { get; set; }

        /// <summary>
        /// The type of discount that the coupon applies. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:CouponDiscountType'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "discountType")]
        public string DiscountType { get; set; }

        /// <summary>
        /// A description of the coupon.<br><br><span class="tablenote"><b>Note:</b> The value returned in the <code>termsWebUrl</code> field should appear for all experiences when displaying coupons. The value in the <code>availableCoupons.message</code> field must also be included if returned in the API response.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "message")]
        public string Message { get; set; }

        /// <summary>
        /// The coupon code.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "redemptionCode")]
        public string RedemptionCode { get; set; }

        /// <summary>
        /// The URL to the coupon terms of use.<br><br><span class="tablenote"><b>Note:</b> The value returned in the <code>termsWebUrl</code> field should appear for all experiences when displaying coupons. The value in the <code>availableCoupons.message</code> field must also be included if returned in the API response.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "termsWebUrl")]
        public string TermsWebUrl { get; set; }
    }

    /// <summary>
    /// This type is used to provide the expiration date of a coded coupon.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class CouponConstraint
    {

        /// <summary>
        /// This timestamp provides the expiration date of the coded coupon.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "expirationDate")]
        public string ExpirationDate { get; set; }
    }

    /// <summary>
    /// The container that defines the fields for the buying options refinements. This container is returned when <code>fieldgroups</code> is set to <code>BUYING_OPTION_REFINEMENTS</code> or <code>FULL</code> in the request.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class BuyingOptionDistribution
    {

        /// <summary>
        /// The container that returns the buying option type. This will be AUCTION, FIXED_PRICE, CLASSIFIED_AD, or a combination of these options. For details, see <a href="/api-docs/buy/browse/resources/item_summary/methods/search#response.itemSummaries.buyingOptions">buyingOptions</a>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "buyingOption")]
        public string BuyingOption { get; set; }

        /// <summary>
        /// The number of items having this buying option.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "matchCount")]
        public System.Nullable<System.Int32> MatchCount { get; set; }

        /// <summary>
        /// The HATEOAS reference for this buying option.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "refinementHref")]
        public string RefinementHref { get; set; }
    }

    /// <summary>
    /// This type is used by the <code>categories</code> container in the response of the <b>search</b>  method, and contains the name and ID of the item category.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class Category
    {

        /// <summary>
        /// The unique identifier of the category.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "categoryId")]
        public string CategoryId { get; set; }

        /// <summary>
        /// The name of the category.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "categoryName")]
        public string CategoryName { get; set; }
    }

    /// <summary>
    /// The container that defines the fields for the category refinements. This container is returned when <code>fieldgroups</code> is set to <code>CATEGORY_REFINEMENTS</code> or <code>FULL</code> in the request.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class CategoryDistribution
    {

        /// <summary>
        /// The unique identifier of the category.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "categoryId")]
        public string CategoryId { get; set; }

        /// <summary>
        /// The name of the category, such as <b>Baby &amp; Toddler Clothing</b>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "categoryName")]
        public string CategoryName { get; set; }

        /// <summary>
        /// The number of items in this category.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "matchCount")]
        public System.Nullable<System.Int32> MatchCount { get; set; }

        /// <summary>
        /// The HATEOAS reference of this category.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "refinementHref")]
        public string RefinementHref { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the <code>item_id</code> values that all use a common description. Often the item variations within an item group all have the same description. Instead of repeating this description in the item details of each item, a description that is shared by at least one other item is returned in this container. If the description is unique, it is returned in the <code>items.description</code> field.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class CommonDescriptions
    {

        /// <summary>
        /// The item description that is used by more than one of the item variations.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "description")]
        public string Description { get; set; }

        /// <summary>
        /// A list of item ids that have this description.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemIds")]
        public string[] ItemIds { get; set; }
    }

    /// <summary>
    /// This type is used to provide contact information for the manufacturer of the product.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class CompanyAddress
    {

        /// <summary>
        /// The first line of the product manufacturer's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "addressLine1")]
        public string AddressLine1 { get; set; }

        /// <summary>
        /// The second line of the product manufacturer's street address. This field is not always used, but can be used for secondary address information such as 'Suite Number' or 'Apt Number'.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "addressLine2")]
        public string AddressLine2 { get; set; }

        /// <summary>
        /// The city of the product manufacturer's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "city")]
        public string City { get; set; }

        /// <summary>
        /// The company name of the product manufacturer.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "companyName")]
        public string CompanyName { get; set; }

        /// <summary>
        /// The contact URL of the product manufacturer.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "contactUrl")]
        public string ContactUrl { get; set; }

        /// <summary>
        /// The two-letter <a href="https://www.iso.org/iso-3166-country-codes.html " target="_blank">ISO 3166</a> standard code for the country of the address. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/ba:CountryCodeEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "country")]
        public string Country { get; set; }

        /// <summary>
        /// The country name of the product manufacturer's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "countryName")]
        public string CountryName { get; set; }

        /// <summary>
        /// The county of the product manufacturer's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "county")]
        public string County { get; set; }

        /// <summary>
        /// The product manufacturer's business email address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "email")]
        public string Email { get; set; }

        /// <summary>
        /// The product manufacturer's business phone number.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "phone")]
        public string Phone { get; set; }

        /// <summary>
        /// The postal code of the product manufacturer's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "postalCode")]
        public string PostalCode { get; set; }

        /// <summary>
        /// The state or province of the product manufacturer's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "stateOrProvince")]
        public string StateOrProvince { get; set; }
    }

    /// <summary>
    /// An array of attribute name/value pairs used to define a specific product. For example: If you wanted to specify a specific car, one of the name/value pairs would be:<br><br><code>"name" : "Year",<br>"value" : "2019"</code><br><br>For a list of the attributes required for cars and trucks and motorcycles refer to <a href="/api-docs/buy/static/api-browse.html#Check" target="_blank">Check compatibility</a> in the <a href="/api-docs/buy/static/buying-ig-landing.html" target="_blank">Buying Integration Guide</a>.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class CompatibilityPayload
    {

        /// <summary>
        /// An array of attribute name/value pairs used to define a specific product. For example: If you wanted to specify a specific car, one of the name/value pairs would be<br><br><code>"name" : "Year", <br>"value" : "2019"</code><br><br>For a list of the attributes required for cars and trucks and motorcycles see <a href="/api-docs/buy/static/api-browse.html#Check">Check compatibility</a> in the Buy Integration Guide.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "compatibilityProperties")]
        public AttributeNameValue[] CompatibilityProperties { get; set; }
    }

    /// <summary>
    /// This container returns the product attribute name/value pairs that are compatible with the keyword. These attributes are submitted in the <code>compatibility_filter</code> request field.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class CompatibilityProperty
    {

        /// <summary>
        /// The name of the product attribute that as been translated to the language of the site.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "localizedName")]
        public string LocalizedName { get; set; }

        /// <summary>
        /// The name of the product attribute, such as Make, Model, Year, etc.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "name")]
        public string Name { get; set; }

        /// <summary>
        /// The value for the <code>name</code> attribute, such as <b>BMW</b>, <b>R1200GS</b>, <b>2011</b>, etc.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "value")]
        public string Value { get; set; }
    }

    /// <summary>
    /// The type that defines the response fields for <b> checkCompatibility</b>.  
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class CompatibilityResponse
    {

        /// <summary>
        /// An enumeration value that tells you if the item is compatible with the product. <br><br>The values are: <ul>   <li>   <b> COMPATIBLE</b> - Indicates the item is compatible with the product specified in the request.</li>   <li>   <b> NOT_COMPATIBLE</b> - Indicates the item is not compatible with the product specified in the request. Be sure to check all the <b> value</b> fields to ensure they are correct as errors in the value can also cause this response.</li>   <li> <b> UNDETERMINED</b> - Indicates one or more attributes for the specified product are missing so compatibility cannot be determined.  The response returns the attributes that are missing.</li>  </ul>  Code so that your app gracefully handles any future changes to this list. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:CompatibilityStatus'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "compatibilityStatus")]
        public string CompatibilityStatus { get; set; }

        /// <summary>
        /// An array of warning messages. These types of errors do not prevent the method from executing but should be checked.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "warnings")]
        public Error[] Warnings { get; set; }
    }

    /// <summary>
    /// The type that defines the fields that can be returned in an error.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class Error
    {

        /// <summary>
        /// This string value indicates the error category. There are three categories of errors: <i>request errors</i>, <i>application errors</i>, and <i>system errors</i>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "category")]
        public string Category { get; set; }

        /// <summary>
        /// The name of the primary system where the error occurred. This is relevant for application errors.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "domain")]
        public string Domain { get; set; }

        /// <summary>
        /// A unique code that identifies the particular error or warning that occurred. Your application can use error codes as identifiers in your customized error-handling algorithms.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "errorId")]
        public System.Nullable<System.Int32> ErrorId { get; set; }

        /// <summary>
        /// An array of reference IDs that identify the specific request elements most closely associated to the error or warning, if any.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "inputRefIds")]
        public string[] InputRefIds { get; set; }

        /// <summary>
        /// A detailed description of the condition that caused the error or warning, and information on what to do to correct the problem.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "longMessage")]
        public string LongMessage { get; set; }

        /// <summary>
        /// A description of the condition that caused the error or warning.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "message")]
        public string Message { get; set; }

        /// <summary>
        /// An array of reference IDs that identify the specific response elements most closely associated to the error or warning, if any.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "outputRefIds")]
        public string[] OutputRefIds { get; set; }

        /// <summary>
        /// An array of warning and error messages that return one or more variables contextual information about the error or warning. This is often the field or value that triggered the error or warning.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "parameters")]
        public ErrorParameter[] Parameters { get; set; }

        /// <summary>
        /// The name of the subdomain in which the error or warning occurred.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "subdomain")]
        public string Subdomain { get; set; }
    }

    /// <summary>
    /// An array of name/value pairs that provide details regarding the error.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ErrorParameter
    {

        /// <summary>
        /// This is the name of input field that caused an issue with the call request.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "name")]
        public string Name { get; set; }

        /// <summary>
        /// This is the actual value that was passed in for the element specified in the <code>name</code> field.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "value")]
        public string Value { get; set; }
    }

    /// <summary>
    /// This type displays additional information about the condition of an item in a structured format.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ConditionDescriptor
    {

        /// <summary>
        /// The name of a condition descriptor. The value(s) for this condition descriptor is returned in the associated <b>values</b> array.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "name")]
        public string Name { get; set; }

        /// <summary>
        /// This array displays the value(s) for a condition descriptor (denoted by the associated <b>name</b> field), as well as any other additional information about the condition of the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "values")]
        public ConditionDescriptorValue[] Values { get; set; }
    }

    /// <summary>
    /// This type displays the value(s) associated with the specified condition descriptor name, as well as any additional information about a condition descriptor. 
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ConditionDescriptorValue
    {

        /// <summary>
        /// Additional information about the condition of an item as it relates to a condition descriptor. This array elaborates on the value specified in the <b>content</b> field and provides additional details about the condition of an item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "additionalInfo")]
        public string[] AdditionalInfo { get; set; }

        /// <summary>
        /// The value for the condition descriptor indicated in the associated <b>name</b> field.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "content")]
        public string Content { get; set; }
    }

    /// <summary>
    /// The container that defines the fields for the conditions refinements. This container is returned when <code>fieldgroups</code> is set to <code>CONDITION_REFINEMENTS</code> or <code>FULL</code> in the request.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ConditionDistribution
    {

        /// <summary>
        /// The text describing the condition of the item, such as <i>New</i> or <i>Used</i>. For a list of condition names, refer to <a href="/api-docs/sell/static/metadata/condition-id-values.html " target="_blank">Item Condition IDs and Names</a>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "condition")]
        public string Condition { get; set; }

        /// <summary>
        /// The identifier of the condition. For example, <code>1000</code> is the identifier for <code>NEW</code>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "conditionId")]
        public string ConditionId { get; set; }

        /// <summary>
        /// The number of items having the condition.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "matchCount")]
        public System.Nullable<System.Int32> MatchCount { get; set; }

        /// <summary>
        /// The HATEOAS reference of this condition.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "refinementHref")]
        public string RefinementHref { get; set; }
    }

    /// <summary>
    /// An array of containers with the details for all of the items returned.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class CoreItem
    {

        /// <summary>
        /// An array of containers with the URLs for the images that are in addition to the primary image.  The primary image is returned in the <b> image.imageUrl</b> field.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "additionalImages")]
        public Image[] AdditionalImages { get; set; }

        /// <summary>
        /// This indicates if the item is for  adults only. For more information about adult-only items on eBay, see <a href="https://pages.ebay.com/help/policies/adult-only.html " target="_blank">Adult items policy</a> for sellers and <a href="https://www.ebay.com/help/terms-conditions/default/searching-adult-items?id=4661 " target="_blank">Adult-Only items on eBay</a> for buyers.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "adultOnly")]
        public System.Nullable<System.Boolean> AdultOnly { get; set; }

        /// <summary>
        /// (Primary Item Aspect) The age group for which the product is recommended. For example, newborn, infant, toddler, kids, adult, etc. All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "ageGroup")]
        public string AgeGroup { get; set; }

        /// <summary>
        /// A type that identifies whether the item is qualified for the Authenticity Guarantee program.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "authenticityGuarantee")]
        public AuthenticityGuaranteeProgram AuthenticityGuarantee { get; set; }

        /// <summary>
        /// A type that identifies whether the item is from a verified seller.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "authenticityVerification")]
        public AuthenticityVerificationProgram AuthenticityVerification { get; set; }

        /// <summary>
        /// A list of available coupons for the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "availableCoupons")]
        public AvailableCoupon[] AvailableCoupons { get; set; }

        /// <summary>
        /// This integer value indicates the total number of bids that have been placed against an auction item. This field is returned only for auction items.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "bidCount")]
        public System.Nullable<System.Int32> BidCount { get; set; }

        /// <summary>
        /// (Primary Item Aspect) The name brand of the item, such as Nike, Apple, etc.  All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "brand")]
        public string Brand { get; set; }

        /// <summary>
        /// A comma separated list of all the purchase options available for the item. The values returned are:<ul><li><code>FIXED_PRICE</code> - Indicates the buyer can purchase the item for a set price using the Buy It Now button.</li><li><code>AUCTION</code> - Indicates the buyer can place a bid for the item. After the first bid is placed, this becomes a live auction item and is the only buying option for this item.</li><li><code>BEST_OFFER</code> - Indicates the buyer can send the seller a price they're willing to pay for the item. The seller can accept, reject, or send a counter offer. For more information on how this works, see <a href="https://www.ebay.com/help/buying/buy-now/making-best-offer?id=4019 ">Making a Best Offer</a>.</li><li><code>CLASSIFIED_AD</code> - Indicates that the final sales transaction is to be completed outside of the eBay environment.</li></ul>Code so that your app gracefully handles any future changes to this list.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "buyingOptions")]
        public string[] BuyingOptions { get; set; }

        /// <summary>
        /// The ID of the leaf category for this item. A leaf category is the lowest level in that category and has no children.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "categoryId")]
        public string CategoryId { get; set; }

        /// <summary>
        /// Text that shows the category hierarchy of the item. For example: Computers/Tablets &amp; Networking, Laptops &amp; Netbooks, PC Laptops &amp; Netbooks
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "categoryPath")]
        public string CategoryPath { get; set; }

        /// <summary>
        /// (Primary Item Aspect) Text describing the color of the item.  All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "color")]
        public string Color { get; set; }

        /// <summary>
        /// A short text description for the condition of the item, such as New or Used. For a list of condition names, see <a href="/api-docs/sell/static/metadata/condition-id-values.html " target="_blank">Item Condition IDs and Names</a>.  <br><br>Code so that your app gracefully handles any future changes to this list.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "condition")]
        public string Condition { get; set; }

        /// <summary>
        /// A full text description for the condition of the item. This field elaborates on the value specified in the <b>condition</b> field and provides full details for the condition of the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "conditionDescription")]
        public string ConditionDescription { get; set; }

        /// <summary>
        /// The identifier of the condition of the item. For example, 1000 is the identifier for NEW. For a list of condition names and IDs, see <a href="/api-docs/sell/static/metadata/condition-id-values.html " target="_blank">Item Condition IDs and Names</a>. <br><br>Code so that your app gracefully handles any future changes to this list.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "conditionId")]
        public string ConditionId { get; set; }

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "currentBidPrice")]
        public ConvertedAmount CurrentBidPrice { get; set; }

        /// <summary>
        /// The full description of the item that was created by the seller. This can be plain text or rich content and can be very large.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "description")]
        public string Description { get; set; }

        /// <summary>
        /// This field indicates if the item can be purchased using the Buy <a href="/api-docs/buy/order/resources/methods">Order API</a>. <ul> <li>If the value of this field is <code>true</code>, this indicates that the item can be purchased using the <b> Order API</b>. </li>  <li>If the value of this field is <code>false</code>, this indicates that the item cannot be purchased using the <b> Order API</b> and must be purchased on the eBay site.</li> </ul>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "eligibleForInlineCheckout")]
        public System.Nullable<System.Boolean> EligibleForInlineCheckout { get; set; }

        /// <summary>
        /// This indicates if the item can be purchased using Guest Checkout in the <a href="/api-docs/buy/order/resources/methods">Order API</a>. You can use this flag to exclude items from your inventory that are not eligible for Guest Checkout, such as gift cards.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "enabledForGuestCheckout")]
        public System.Nullable<System.Boolean> EnabledForGuestCheckout { get; set; }

        /// <summary>
        /// This indicates the <a href="https://en.wikipedia.org/wiki/European_Union_energy_label ">European energy efficiency</a> rating (EEK) of the item. This field is returned only if the seller specified the energy efficiency rating. <br><br>The rating is a set of energy efficiency classes from A to G, where 'A' is the most energy efficient and 'G' is the least efficient. This rating helps buyers choose between various models. <br><br>When the manufacturer's specifications for this item are available, the link to this information is returned in the <b>productFicheWebUrl</b> field.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "energyEfficiencyClass")]
        public string EnergyEfficiencyClass { get; set; }

        /// <summary>
        /// An EPID is the eBay product identifier of a product from the eBay product catalog.  This indicates the product in which the item belongs.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "epid")]
        public string Epid { get; set; }

        /// <summary>
        /// The estimated number of this item that are available for purchase. Because the quantity of an item can change several times within a second, it is impossible to return the exact quantity. So instead of returning quantity, the estimated availability of the item is returned.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "estimatedAvailabilities")]
        public EstimatedAvailability[] EstimatedAvailabilities { get; set; }

        /// <summary>
        /// (Primary Item Aspect) The gender for the item. This is used for items that could vary by gender, such as clothing. For example: male, female, or unisex. All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "gender")]
        public string Gender { get; set; }

        /// <summary>
        /// The unique Global Trade Item number of the item as defined by <a href="https://www.gtin.info " target="_blank">https://www.gtin.info</a>. This can be a UPC (Universal Product Code), EAN (European Article Number), or an ISBN (International Standard Book Number) value.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "gtin")]
        public string Gtin { get; set; }

        /// <summary>
        /// Type that defines the details of an image, such as size and image URL. Currently, only <code>imageUrl</code> is populated. The <code>height</code> and <code>width</code> are reserved for future use.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "image")]
        public Image Image { get; set; }

        /// <summary>
        /// A value of <code>true</code> indicates that the seller requires immediate payment from the buyer when purchasing an item.<br><br><span class="tablenote"><b>Note:</b> It is possible for this field to be set to <code>true</code>, but not apply in some scenarios. For example, immediate payment is not applicable for auction listings that have a winning bidder, for buyers' purchases that involve the Best Offer feature, or for offline transactions.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "immediatePay")]
        public System.Nullable<System.Boolean> ImmediatePay { get; set; }

        /// <summary>
        /// The ePID (eBay Product ID of a product from the eBay product catalog) for the item, which has been programmatically determined by eBay using the item's title, aspects, and other data. <br><br>If the seller provided an ePID for the item, the seller's value is returned in the <b> epid</b> field. <br><br><span class="tablenote"><b> Note: </b> This field is returned only for authorized Partners.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "inferredEpid")]
        public string InferredEpid { get; set; }

        /// <summary>
        /// The URL to the View Item page of the item which includes the affiliate tracking ID.<br><br><span class="tablenote"><b>Note:</b> In order to receive commissions on sales, eBay Partner Network affiliates must use this URL to forward buyers to the listing on the eBay marketplace.</span><br>The <b>itemAffiliateWebUrl</b> is only returned if:<ul><li>The marketplace through which the item is being viewed is part of the eBay Partner Network. Currently Singapore (<code>EBAY_SG</code>) is <b>not</b> supported.<br><br>For additional information, refer to <a href="https://partnerhelp.ebay.com/helpcenter/s/article/countries-available-as-a-program-in-EPN?language=en_US " target="_blank">eBay Partner Network</a>.</li><li>The seller enables affiliate tracking for the item by including the <code><a href="/api-docs/buy/static/api-browse.html#Headers">X-EBAY-C-ENDUSERCTX</a></code> request header in the method.</li></ul>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemAffiliateWebUrl")]
        public string ItemAffiliateWebUrl { get; set; }

        /// <summary>
        /// A timestamp that indicates the date and time an item listing was created.<br><br>This value is returned in UTC format (yyyy-MM-ddThh:mm:ss.sssZ), which can be converted into the local time of the buyer.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemCreationDate")]
        public string ItemCreationDate { get; set; }

        /// <summary>
        /// A timestamp that indicates the date and time an auction listing will end.<br><br>If a fixed-price listing has ended, this field indicates the date and time the listing ended.<br><br>This value is returned in UTC format (<code>yyyy-MM-ddThh:mm:ss.sssZ</code>), which can be converted into the local time of the buyer.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemEndDate")]
        public string ItemEndDate { get; set; }

        /// <summary>
        /// The unique RESTful identifier of the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemId")]
        public string ItemId { get; set; }

        /// <summary>
        /// The type that defines the fields for an address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemLocation")]
        public Address ItemLocation { get; set; }

        /// <summary>
        /// The URL of the View Item page of the item. This enables you to include a "Report Item on eBay" link that takes the buyer to the View Item page on eBay. From there they can report any issues regarding this item to eBay.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemWebUrl")]
        public string ItemWebUrl { get; set; }

        /// <summary>
        /// The unique identifier of the eBay listing that contains the item. This is the traditional/legacy ID that is often seen in the URL of the listing View Item page.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "legacyItemId")]
        public string LegacyItemId { get; set; }

        /// <summary>
        /// An array of containers that show the complete list of the aspect name/value pairs that describe the variation of the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "localizedAspects")]
        public TypedNameValue[] LocalizedAspects { get; set; }

        /// <summary>
        /// The number of items in a lot. In other words, a lot size is the number of items that are being sold together.  <br><br>A lot is a set of two or more items included in a single listing that must be purchased together in a single order line item. All the items in the lot are the same but there can be multiple items in a single lot,  such as the package of batteries shown in the example below.   <br><br><table border="1"> <tr> <tr>  <th>Item</th>  <th>Lot Definition</th> <th>Lot Size</th></tr>  <tr>  <td>A package of 24 AA batteries</td>  <td>A box of 10 packages</td>  <td>10  </td> </tr>  <tr>  <td>A P235/75-15 Goodyear tire </td>  <td>4 tires  </td>  <td>4  </td> </tr> <tr> <td>Fashion Jewelry Rings  </td> <td>Package of 100 assorted rings  </td> <td>100 </td> </tr></table>  <br><br><span class="tablenote"><b>Note: </b>  Lots are not supported in all categories.  </span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "lotSize")]
        public System.Nullable<System.Int32> LotSize { get; set; }

        /// <summary>
        /// The type that defines the fields that describe a seller discount.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "marketingPrice")]
        public MarketingPrice MarketingPrice { get; set; }

        /// <summary>
        /// (Primary Item Aspect) Text describing what the item is made of. For example, silk. All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "material")]
        public string Material { get; set; }

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "minimumPriceToBid")]
        public ConvertedAmount MinimumPriceToBid { get; set; }

        /// <summary>
        /// The manufacturer's part number, which is a unique number that identifies a specific product. To identify the product, this is always used along with brand.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "mpn")]
        public string Mpn { get; set; }

        /// <summary>
        /// (Primary Item Aspect) Text describing the pattern used on the item. For example, paisley. All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "pattern")]
        public string Pattern { get; set; }

        /// <summary>
        /// The payment methods for the item, including the payment method types, brands, and instructions for the buyer.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "paymentMethods")]
        public PaymentMethod[] PaymentMethods { get; set; }

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "price")]
        public ConvertedAmount Price { get; set; }

        /// <summary>
        /// Indicates when in the buying flow the item's price can appear for minimum advertised price (MAP) items, which is the lowest price a retailer can advertise/show for this item. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:PriceDisplayConditionEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "priceDisplayCondition")]
        public string PriceDisplayCondition { get; set; }

        /// <summary>
        /// The type that defines the fields for the details of each item in an item group. An item group is  an item that has various aspect differences, such as color, size, storage capacity, etc. When an item group is created, one of the item variations, such as the red shirt size L, is chosen as the "parent". All the other items in the group are the children, such as the blue shirt size L, red shirt size M, etc. <br><br><span class="tablenote"><b> Note: </b> This container is returned only if the <b> item_id</b> in the request is an item group (parent ID of an item with variations).</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "primaryItemGroup")]
        public ItemGroupSummary PrimaryItemGroup { get; set; }

        /// <summary>
        /// The type that defines the fields for the rating of a product review.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "primaryProductReviewRating")]
        public ReviewRating PrimaryProductReviewRating { get; set; }

        /// <summary>
        /// This field is returned as <code>true</code> if the listing is part of a Promoted Listing campaign. Promoted Listings are available to Above Standard and Top Rated sellers with recent sales activity.<br><br>For more information, see <a href="https://pages.ebay.com/seller-center/listing-and-marketing/promoted-listings.html " target="_blank">Promoted Listings</a>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "priorityListing")]
        public System.Nullable<System.Boolean> PriorityListing { get; set; }

        /// <summary>
        /// The type that defines the fields for the product information of the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "product")]
        public Product Product { get; set; }

        /// <summary>
        /// The URL of a page containing the manufacturer's specification of this item, which helps buyers make a purchasing decision. This information is available only for items that include the European energy efficiency rating (EEK) but is not available for <em> all</em> items with an EEK rating and is returned only if this information is available. The EEK rating of the item is returned in the <b> energyEfficiencyClass</b> field.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "productFicheWebUrl")]
        public string ProductFicheWebUrl { get; set; }

        /// <summary>
        /// An array of the qualified programs available for the item, such as EBAY_PLUS, AUTHENTICITY_GUARANTEE, and AUTHENTICITY_VERIFICATION.<br><br>eBay Plus is a premium account option for buyers, which provides benefits such as fast free domestic shipping and free returns on selected items. Top-Rated eBay sellers must opt in to eBay Plus to be able to offer the program on qualifying listings. Sellers must commit to next-day delivery of those items.<br><br><span class="tablenote"><b>Note: </b> eBay Plus is available only to buyers in Germany, Austria, and Australia marketplaces.</span><br><br>The eBay <a href="https://pages.ebay.com/authenticity-guarantee/ " target="_blank">Authenticity Guarantee</a> program enables third-party authenticators to perform authentication verification inspections on items such as watches and sneakers.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "qualifiedPrograms")]
        public string[] QualifiedPrograms { get; set; }

        /// <summary>
        /// The maximum number for a specific item that one buyer can purchase.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "quantityLimitPerBuyer")]
        public System.Nullable<System.Int32> QuantityLimitPerBuyer { get; set; }

        /// <summary>
        /// This indicates if the reserve price of the item has been met. A reserve price is set by the seller and is the minimum amount the seller is willing to sell the item for. <p>If the highest bid is not equal to or higher than the reserve price when the auction ends, the listing ends and the item is not sold.</p> <p><b> Note: </b>This is returned only for auctions that have a reserve price.</p>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "reservePriceMet")]
        public System.Nullable<System.Boolean> ReservePriceMet { get; set; }

        /// <summary>
        /// The type that defines the fields for the seller's return policy.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "returnTerms")]
        public ItemReturnTerms ReturnTerms { get; set; }

        /// <summary>
        /// The type that defines the fields for basic and detailed information about the seller of the item returned by the <b> item</b> resource.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "seller")]
        public SellerDetail Seller { get; set; }

        /// <summary>
        /// An identifier generated/incremented when a seller revises the item. There are two types of item revisions: <ul><li>Seller changes, such as changing the title</li>  <li>eBay system changes, such as changing the quantity when an item is purchased</li></ul> This ID is changed <em> only</em> when the seller makes a change to the item. This means you cannot use this value to determine if the quantity has changed.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "sellerItemRevision")]
        public string SellerItemRevision { get; set; }

        /// <summary>
        /// An array of shipping options containers that have the details about cost, carrier, etc. of one shipping option.<br><br><span class="tablenote"><b>Note:</b> For items with calculated shipping, this array is only returned if the <b>X-EBAY-C-ENDUSERCTX</b> header is supplied.</span> 
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "shippingOptions")]
        public ShippingOption[] ShippingOptions { get; set; }

        /// <summary>
        /// The type that defines the fields that include and exclude geographic regions affecting where the item can be shipped. The seller defines these regions when listing the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "shipToLocations")]
        public ShipToLocations ShipToLocations { get; set; }

        /// <summary>
        /// This text string is derived from the item condition and the item aspects (such as size, color, capacity, model, brand, etc.).
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "shortDescription")]
        public string ShortDescription { get; set; }

        /// <summary>
        /// (Primary Item Aspect) The size of the item. For example, '7' for a size 7 shoe. All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "size")]
        public string Size { get; set; }

        /// <summary>
        /// (Primary Item Aspect) The sizing system of the country.  All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container. <br><br><b> Valid Values: </b> <br>AU (Australia),  <br>BR (Brazil), <br>CN (China),  <br>DE (Germany),  <br>EU (European Union),  <br> FR (France), <br> IT (Italy),  <br>JP (Japan), <br>MX (Mexico),  <br>US (USA), <br> UK (United Kingdom) <br><br>Code so that your app gracefully handles any future changes to this list. 
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "sizeSystem")]
        public string SizeSystem { get; set; }

        /// <summary>
        /// (Primary Item Aspect) Text describing a size group in which the item would be included, such as regular, petite, plus, big-and-tall or maternity. All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "sizeType")]
        public string SizeType { get; set; }

        /// <summary>
        /// A subtitle is optional and allows the seller to provide more information about the product, possibly including keywords that may assist with search results.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "subtitle")]
        public string Subtitle { get; set; }

        /// <summary>
        /// The container for the tax information for the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "taxes")]
        public Taxes[] Taxes { get; set; }

        /// <summary>
        /// The seller-created title of the item. <br><br><b> Maximum Length: </b> 80 characters
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "title")]
        public string Title { get; set; }

        /// <summary>
        /// This indicates if the item a top-rated plus item. There are three benefits of a top-rated plus item: a  minimum 30-day money-back return policy, shipping the items in 1 business day with tracking provided, and the added comfort of knowing this item is from experienced sellers with the highest buyer ratings. See the <a href="https://pages.ebay.com/topratedplus/index.html " target="_blank">Top Rated Plus Items </a> and <a href="https://pages.ebay.com/help/sell/top-rated.html " target="_blank">Becoming a Top Rated Seller and qualifying for Top Rated Plus</a> help topics for more information.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "topRatedBuyingExperience")]
        public System.Nullable<System.Boolean> TopRatedBuyingExperience { get; set; }

        /// <summary>
        /// The URL to the image that shows the information on the tyre label.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "tyreLabelImageUrl")]
        public string TyreLabelImageUrl { get; set; }

        /// <summary>
        /// This integer value indicates the number of different eBay users who have placed one or more bids on an auction item. This field is only applicable to auction items.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "uniqueBidderCount")]
        public System.Nullable<System.Int32> UniqueBidderCount { get; set; }

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "unitPrice")]
        public ConvertedAmount UnitPrice { get; set; }

        /// <summary>
        /// The designation, such as size, weight, volume, count, etc., that was used to specify the quantity of the item.  This helps buyers compare prices. <br><br>For example, the following tells the buyer that the item is 7.99 per 100 grams. <br><br><code>"unitPricingMeasure": "100g",<br> "unitPrice": {<br>&nbsp;&nbsp;"value": "7.99",<br>&nbsp;&nbsp;"currency": "GBP"</code>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "unitPricingMeasure")]
        public string UnitPricingMeasure { get; set; }
    }

    /// <summary>
    /// Type that defines the details of an image, such as size and image URL. Currently, only <code>imageUrl</code> is populated. The <code>height</code> and <code>width</code> are reserved for future use.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class Image
    {

        /// <summary>
        /// Reserved for future use.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "height")]
        public System.Nullable<System.Int32> Height { get; set; }

        /// <summary>
        /// The URL of the image.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "imageUrl")]
        public string ImageUrl { get; set; }

        /// <summary>
        /// Reserved for future use.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "width")]
        public System.Nullable<System.Int32> Width { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the estimated item availability information.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class EstimatedAvailability
    {

        /// <summary>
        /// This field is return only when the seller sets their '<a href="#display-item-quantity">display item quantity</a>' preference to <b> Display "More than 10 available" in your listing (if applicable)</b>. The value of this field will be "10", which is the threshold value. <br><br>Code so that your app gracefully handles any future changes to this value.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "availabilityThreshold")]
        public System.Nullable<System.Int32> AvailabilityThreshold { get; set; }

        /// <summary>
        /// <a name="display-item-quantity"></a> This field is return only when the seller sets their <b> Display Item Quantity</b> preference to <b> Display "More than 10 available" in your listing (if applicable)</b>. The value of this field will be <code> MORE_THAN</code>. This indicates that the seller has more than the 'quantity display preference', which is 10, in stock for this item.    <br><br> The following are the display item quantity preferences the seller can set. <br><ul><li> <b> Display "More than 10 available" in your listing (if applicable) </b><ul> <li>If the seller enables this preference, this field is returned as long as there are more than 10 of this item in inventory.</li>  <li> If the quantity is equal to 10 or drops below 10, this field is not returned and the estimated quantity of the item is returned in the <b> estimatedAvailableQuantity</b> field.</li></ul> </li> <li> <b> Display the exact quantity in your items</b> <br>If the seller enables this preference, the <b> availabilityThresholdType</b> and <b> availabilityThreshold</b> fields are not returned and the estimated quantity of the item is returned in the <b> estimatedAvailableQuantity</b> field.<br><br><b> Note: </b> Because the quantity of an item can change several times within a second, it is impossible to return the exact quantity. </li></ul>   <br>Code so that your app gracefully handles any future changes to these preferences. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:AvailabilityThresholdEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "availabilityThresholdType")]
        public string AvailabilityThresholdType { get; set; }

        /// <summary>
        /// An array of available delivery options. <br><br><b> Valid Values: </b> SHIP_TO_HOME, SELLER_ARRANGED_LOCAL_PICKUP, IN_STORE_PICKUP, PICKUP_DROP_OFF, or DIGITAL_DELIVERY <br><br>Code so that your app gracefully handles any future changes to this list. 
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "deliveryOptions")]
        public string[] DeliveryOptions { get; set; }

        /// <summary>
        /// An enumeration value representing the inventory status of this item.<br><br><span class="tablenote"><b> Note: </b>Be sure to review the <b>itemEndDate</b> field to determine whether the item is available for purchase.</span><br><br><b> Valid Values: </b> IN_STOCK, LIMITED_STOCK, or OUT_OF_STOCK <br><br>Code so that your app gracefully handles any future changes to this list. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:AvailabilityStatusEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "estimatedAvailabilityStatus")]
        public string EstimatedAvailabilityStatus { get; set; }

        /// <summary>
        /// The estimated number of this item that are available for purchase. Because the quantity of an item can change several times within a second, it is impossible to return the exact quantity. So instead of returning quantity, the estimated availability of the item is returned.<br><br><span class="tablenote"><b>Note:</b> To see if a listing is available for purchase, review the <a href="/api-docs/buy/browse/resources/item/methods/getItem#response.itemEndDate">itemEndDate</a> and <a href="/api-docs/buy/browse/resources/item/methods/getItem#response.estimatedAvailabilities.estimatedAvailabilityStatus">estimatedAvailablityStatus</a> fields. If the item has an <b>EndDate</b> in the past, or the <b>estimatedAvailabilityStatus</b> is <code>OUT_OF_STOCK</code>, the item is unavailable for purchase.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "estimatedAvailableQuantity")]
        public System.Nullable<System.Int32> EstimatedAvailableQuantity { get; set; }

        /// <summary>
        /// The estimated number of this item that are available for purchase. Because the quantity of an item can change several times within a second, it is impossible to return the exact quantity. So instead of returning quantity, the estimated availability of the item is returned.<br><br><span class="tablenote"><b>Note:</b> To see if a listing is available for purchase, review the <a href="/api-docs/buy/browse/resources/item/methods/getItem#response.itemEndDate">itemEndDate</a> and <a href="/api-docs/buy/browse/resources/item/methods/getItem#response.estimatedAvailabilities.estimatedAvailabilityStatus">estimatedAvailablityStatus</a> fields. If the item has an <b>EndDate</b> in the past, or the <b>estimatedAvailabilityStatus</b> is <code>OUT_OF_STOCK</code>, the item is unavailable for purchase.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "estimatedRemainingQuantity")]
        public System.Nullable<System.Int32> EstimatedRemainingQuantity { get; set; }

        /// <summary>
        /// The estimated number of this item that have been sold.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "estimatedSoldQuantity")]
        public System.Nullable<System.Int32> EstimatedSoldQuantity { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the name/value pairs for item aspects.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class TypedNameValue
    {

        /// <summary>
        /// The text representing the name of the aspect for the name/value pair, such as Color.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "name")]
        public string Name { get; set; }

        /// <summary>
        /// This indicates if the value being returned is a string or an array of values. <br><br><b> Valid Values: </b> <ul><li><b> STRING</b> - Indicates the value returned is a string.</li>  <li><b> STRING_ARRAY</b> - Indicates the value returned is an array of strings.</li></ul>  Code so that your app gracefully handles any future changes to this list. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:ValueTypeEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "type")]
        public string Type { get; set; }

        /// <summary>
        /// The value of the aspect for the name/value pair, such as Red.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "value")]
        public string Value { get; set; }
    }

    /// <summary>
    /// The type that defines the fields that describe a seller discount.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class MarketingPrice
    {

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "discountAmount")]
        public ConvertedAmount DiscountAmount { get; set; }

        /// <summary>
        /// This field expresses the percentage of the seller discount based on the value in the <code>originalPrice</code> container.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "discountPercentage")]
        public string DiscountPercentage { get; set; }

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "originalPrice")]
        public ConvertedAmount OriginalPrice { get; set; }

        /// <summary>
        /// Indicates the pricing treatment (discount) that was applied to the price of the item.<br><br><span class="tablenote"><b>Note:</b> The pricing treatment affects the way and where the discounted price can be displayed.</span> For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:PriceTreatmentEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "priceTreatment")]
        public string PriceTreatment { get; set; }
    }

    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class PaymentMethod
    {

        /// <summary>
        /// The payment method type, such as credit card or cash. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:PaymentMethodTypeEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "paymentMethodType")]
        public string PaymentMethodType { get; set; }

        /// <summary>
        /// The payment method brands, including the payment method brand type and logo image.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "paymentMethodBrands")]
        public PaymentMethodBrand[] PaymentMethodBrands { get; set; }

        /// <summary>
        /// The payment instructions for the buyer, such as <i>cash in person</i> or <i>contact seller</i>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "paymentInstructions")]
        public string[] PaymentInstructions { get; set; }

        /// <summary>
        /// The seller instructions to the buyer, such as <i>accepts credit cards</i> or <i>see description</i>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "sellerInstructions")]
        public string[] SellerInstructions { get; set; }
    }

    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class PaymentMethodBrand
    {

        /// <summary>
        /// The payment method brand, such as Visa or PayPal. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:PaymentMethodBrandEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "paymentMethodBrandType")]
        public string PaymentMethodBrandType { get; set; }

        /// <summary>
        /// Type that defines the details of an image, such as size and image URL. Currently, only <code>imageUrl</code> is populated. The <code>height</code> and <code>width</code> are reserved for future use.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "logoImage")]
        public Image LogoImage { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the details of each item in an item group. An item group is  an item that has various aspect differences, such as color, size, storage capacity, etc. When an item group is created, one of the item variations, such as the red shirt size L, is chosen as the "parent". All the other items in the group are the children, such as the blue shirt size L, red shirt size M, etc. <br><br><span class="tablenote"><b> Note: </b> This container is returned only if the <b> item_id</b> in the request is an item group (parent ID of an item with variations).</span>
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ItemGroupSummary
    {

        /// <summary>
        /// An array of containers with the URLs for images that are in addition to the primary image of the item group.  The primary image is returned in the <b> itemGroupImage</b> field.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemGroupAdditionalImages")]
        public Image[] ItemGroupAdditionalImages { get; set; }

        /// <summary>
        /// The HATEOAS reference of the parent page of the item group. An item group is an item that has various aspect differences, such as color, size, storage capacity, etc. 
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemGroupHref")]
        public string ItemGroupHref { get; set; }

        /// <summary>
        /// The unique identifier for the item group. An item group is an item that has various aspect differences, such as color, size, storage capacity, etc. 
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemGroupId")]
        public string ItemGroupId { get; set; }

        /// <summary>
        /// Type that defines the details of an image, such as size and image URL. Currently, only <code>imageUrl</code> is populated. The <code>height</code> and <code>width</code> are reserved for future use.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemGroupImage")]
        public Image ItemGroupImage { get; set; }

        /// <summary>
        /// The title of the item that appears on the item group page. An item group is an item that has various aspect differences, such as color, size, storage capacity, etc. 
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemGroupTitle")]
        public string ItemGroupTitle { get; set; }

        /// <summary>
        /// An enumeration value that indicates the type of the item group. An item group is an item that has various aspect differences, such as color, size, storage capacity, etc. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:ItemGroupTypeEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemGroupType")]
        public string ItemGroupType { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the rating of a product review.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ReviewRating
    {

        /// <summary>
        /// The average rating given to a product based on customer reviews.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "averageRating")]
        public string AverageRating { get; set; }

        /// <summary>
        /// An array of containers for the product rating histograms that shows the review counts and the product rating.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "ratingHistograms")]
        public RatingHistogram[] RatingHistograms { get; set; }

        /// <summary>
        /// The total number of reviews for the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "reviewCount")]
        public System.Nullable<System.Int32> ReviewCount { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for product ratings. Only products that are in the eBay product catalog can be reviewed and rated.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class RatingHistogram
    {

        /// <summary>
        /// The total number of user ratings that the product has received.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "count")]
        public System.Nullable<System.Int32> Count { get; set; }

        /// <summary>
        /// This is the average rating for the product. As part of a product review, users rate the product. Products are rated from one star (terrible) to five stars (excellent), with each star having a corresponding point value - one star gets 1 point, two stars get 2 points, and so on. If a product had one four-star rating and one five-star rating, its average rating would be <code> 4.5</code>, and this is the value that would appear in this field.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "rating")]
        public string Rating { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the product information of the item.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class Product
    {

        /// <summary>
        /// An array of containers with the URLs for the product images that are in addition to the primary image. 
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "additionalImages")]
        public Image[] AdditionalImages { get; set; }

        /// <summary>
        /// An array of product identifiers associated with the item. This container is returned if the seller has associated the eBay Product Identifier (ePID) with the item and in the request <b> fieldgroups</b> is set to <code>PRODUCT</code>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "additionalProductIdentities")]
        public AdditionalProductIdentity[] AdditionalProductIdentities { get; set; }

        /// <summary>
        /// An array of containers for the product aspects. Each group contains the aspect group name and the aspect name/value pairs.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "aspectGroups")]
        public AspectGroup[] AspectGroups { get; set; }

        /// <summary>
        /// The brand associated with product. To identify the product, this is always used along with MPN (manufacturer part number).
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "brand")]
        public string Brand { get; set; }

        /// <summary>
        /// The rich description of an eBay product, which might contain HTML.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "description")]
        public string Description { get; set; }

        /// <summary>
        /// An array of all the possible GTINs values associated with the product. A GTIN is a unique Global Trade Item number of the item as defined by <a href="https://www.gtin.info " target="_blank">https://www.gtin.info</a>. This can be a UPC (Universal Product Code), EAN (European Article Number), or an ISBN (International Standard Book Number) value.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "gtins")]
        public string[] Gtins { get; set; }

        /// <summary>
        /// Type that defines the details of an image, such as size and image URL. Currently, only <code>imageUrl</code> is populated. The <code>height</code> and <code>width</code> are reserved for future use.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "image")]
        public Image Image { get; set; }

        /// <summary>
        /// An array of all possible MPN values associated with the product. A MPNs is manufacturer part number of the product. To identify the product, this is always used along with brand.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "mpns")]
        public string[] Mpns { get; set; }

        /// <summary>
        /// The title of the product.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "title")]
        public string Title { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the seller's return policy.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ItemReturnTerms
    {

        /// <summary>
        /// This indicates if the seller has enabled the Extended Holiday Returns feature on the item. Extended Holiday Returns are only applicable during the US holiday season, and gives buyers extra time to return an item. This 'extra time' will typically extend beyond what is set through the <b> returnPeriod</b> value.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "extendedHolidayReturnsOffered")]
        public System.Nullable<System.Boolean> ExtendedHolidayReturnsOffered { get; set; }

        /// <summary>
        /// An enumeration value that indicates how a buyer is refunded when an item is returned. <br><br><b> Valid Values: </b> MONEY_BACK or MERCHANDISE_CREDIT  <br><br>Code so that your app gracefully handles any future changes to this list. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:RefundMethodEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "refundMethod")]
        public string RefundMethod { get; set; }

        /// <summary>
        /// This string field indicates the restocking fee percentage that the seller has set on the item. Sellers have the option of setting no restocking fee for an item, or they can set the percentage to 10, 15, or 20 percent. So, if the cost of the item was $100, and the restocking percentage was 20 percent, the buyer would be charged $20 to return that item, so instead of receiving a $100 refund, they would receive $80 due to the restocking fee.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "restockingFeePercentage")]
        public string RestockingFeePercentage { get; set; }

        /// <summary>
        /// Text written by the seller describing what the buyer needs to do in order to return the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "returnInstructions")]
        public string ReturnInstructions { get; set; }

        /// <summary>
        /// An enumeration value that indicates the alternative methods for a full refund when an item is returned. This field is returned if the seller offers the buyer an item replacement or exchange instead of a monetary refund. <br><br><b> Valid Values: </b>  <ul><li><b> REPLACEMENT</b> -  Indicates that the buyer has the option of receiving money back for the returned item, or they can choose to have the seller replace the item with an identical item.</li>  <li><b> EXCHANGE</b> - Indicates that the buyer has the option of receiving money back for the returned item, or they can exchange the item for another similar item.</li></ul>  Code so that your app gracefully handles any future changes to this list. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:ReturnMethodEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "returnMethod")]
        public string ReturnMethod { get; set; }

        /// <summary>
        /// The type that defines the fields for a period of time in the time-measurement units supplied.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "returnPeriod")]
        public TimeDuration ReturnPeriod { get; set; }

        /// <summary>
        /// Indicates whether the seller accepts returns for the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "returnsAccepted")]
        public System.Nullable<System.Boolean> ReturnsAccepted { get; set; }

        /// <summary>
        /// This enumeration value indicates whether the buyer or seller is responsible for return shipping costs when an item is returned. <br><br><b> Valid Values: </b> <ul><li><b> SELLER</b> - Indicates the seller will pay for the shipping costs to return the item.</li>  <li><b> BUYER</b> - Indicates the buyer will pay for the shipping costs to return the item.</li>  </ul>  Code so that your app gracefully handles any future changes to this list. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:ReturnShippingCostPayerEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "returnShippingCostPayer")]
        public string ReturnShippingCostPayer { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for a period of time in the time-measurement units supplied.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class TimeDuration
    {

        /// <summary>
        /// An enumeration value that indicates the units of the time span (e.g., <code>HOURS</code>). The enumeration value in this field defines the period of time being used to measure the duration.<br><br>Refer to <a href="/api-docs/buy/browse/types/ba:TimeDurationUnitEnum">TimeDurationUnitEnum</a> for the list of supported values. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/ba:TimeDurationUnitEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "unit")]
        public string Unit { get; set; }

        /// <summary>
        /// Retrieves the duration of the time span (no units). The value in this field indicates the number of years, months, days, hours, or minutes in the defined period.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "value")]
        public System.Nullable<System.Int32> Value { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for basic and detailed information about the seller of the item returned by the <b> item</b> resource.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class SellerDetail
    {

        /// <summary>
        /// The percentage of the total positive feedback.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "feedbackPercentage")]
        public string FeedbackPercentage { get; set; }

        /// <summary>
        /// The feedback score of the seller. This value is based on the ratings from eBay members that bought items from this seller.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "feedbackScore")]
        public System.Nullable<System.Int32> FeedbackScore { get; set; }

        /// <summary>
        /// This indicates if the seller is a business or an individual. This is determined when the seller registers with eBay. If they register for a business account, this value will be BUSINESS. If they register for a private account, this value will be INDIVIDUAL. This designation is required by the tax laws in the following countries:  <br><br> This field is returned only on the following sites. <br><br>EBAY_AT, EBAY_BE, EBAY_CH, EBAY_DE, EBAY_ES, EBAY_FR, EBAY_GB, EBAY_IE, EBAY_IT, EBAY_PL <br><br><b> Valid Values:</b> BUSINESS or INDIVIDUAL <br><br>Code so that your app gracefully handles any future changes to this list. 
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "sellerAccountType")]
        public string SellerAccountType { get; set; }

        /// <summary>
        /// The type that defines the fields for the contact information for a seller.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "sellerLegalInfo")]
        public SellerLegalInfo SellerLegalInfo { get; set; }

        /// <summary>
        /// The unique identifier of an eBay user across all eBay sites. This value does not change, even when a user changes their username.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "userId")]
        public string UserId { get; set; }

        /// <summary>
        /// The user name created by the seller for use on eBay.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "username")]
        public string Username { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the contact information for a seller.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class SellerLegalInfo
    {

        /// <summary>
        /// The seller's business email address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "email")]
        public string Email { get; set; }

        /// <summary>
        /// The seller' business fax number.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "fax")]
        public string Fax { get; set; }

        /// <summary>
        /// This is a free-form string created by the seller. This is information often found on business cards, such as address. This is information used by some countries.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "imprint")]
        public string Imprint { get; set; }

        /// <summary>
        /// The seller's first name.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "legalContactFirstName")]
        public string LegalContactFirstName { get; set; }

        /// <summary>
        /// The seller's last name.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "legalContactLastName")]
        public string LegalContactLastName { get; set; }

        /// <summary>
        /// The name of the seller's business.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "name")]
        public string Name { get; set; }

        /// <summary>
        /// The seller's business phone number.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "phone")]
        public string Phone { get; set; }

        /// <summary>
        /// The seller's registration number. This is information used by some countries.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "registrationNumber")]
        public string RegistrationNumber { get; set; }

        /// <summary>
        /// Type that defines the fields for the seller's address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "sellerProvidedLegalAddress")]
        public LegalAddress SellerProvidedLegalAddress { get; set; }

        /// <summary>
        /// This is a free-form string created by the seller. This is the seller's terms or condition, which is in addition to the seller's return policies.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "termsOfService")]
        public string TermsOfService { get; set; }

        /// <summary>
        /// An array of the seller's VAT (value added tax) IDs and the issuing country. VAT is a tax added by some European countries.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "vatDetails")]
        public VatDetail[] VatDetails { get; set; }

        /// <summary>
        /// The type that provides required Economic Operator information about the manufacturer and/or supplier of the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "economicOperator")]
        public EconomicOperator EconomicOperator { get; set; }

        /// <summary>
        /// The Waste Electrical and Electronic Equipment (WEEE) registration number required for any seller to place electrical and electronic equipment on the market in Germany. This manufacturer number is assigned to the first distributors of electrical and electronic equipment and comprises a country code and an 8-digit sequence of digits (e.g. “WEEE Reg. No. DE 12345678”).
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "weeeNumber")]
        public string WeeeNumber { get; set; }
    }

    /// <summary>
    /// Type that defines the fields for the seller's address.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class LegalAddress
    {

        /// <summary>
        /// The first line of the street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "addressLine1")]
        public string AddressLine1 { get; set; }

        /// <summary>
        /// The second line of the street address. This field is not always used, but can be used for 'Suite Number' or 'Apt Number'.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "addressLine2")]
        public string AddressLine2 { get; set; }

        /// <summary>
        /// The city of the address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "city")]
        public string City { get; set; }

        /// <summary>
        /// The two-letter <a href="https://www.iso.org/iso-3166-country-codes.html " target="_blank">ISO 3166</a> standard code for the country of the address. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/ba:CountryCodeEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "country")]
        public string Country { get; set; }

        /// <summary>
        /// The name of the country of the address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "countryName")]
        public string CountryName { get; set; }

        /// <summary>
        /// The name of the county of the address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "county")]
        public string County { get; set; }

        /// <summary>
        /// The postal code of the address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "postalCode")]
        public string PostalCode { get; set; }

        /// <summary>
        /// The state or province of the address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "stateOrProvince")]
        public string StateOrProvince { get; set; }
    }

    /// <summary>
    /// The type the defines the fields for the VAT (value add tax) information.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class VatDetail
    {

        /// <summary>
        /// The two-letter <a href="https://www.iso.org/iso-3166-country-codes.html " target="_blank">ISO 3166</a> standard of the country issuing the seller's VAT (value added tax) ID. VAT is a tax added by some European countries. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/ba:CountryCodeEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "issuingCountry")]
        public string IssuingCountry { get; set; }

        /// <summary>
        /// The seller's VAT (value added tax) ID. VAT is a tax added by some European countries.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "vatId")]
        public string VatId { get; set; }
    }

    /// <summary>
    /// The type that provides required Economic Operator information about the manufacturer and/or supplier of the item.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class EconomicOperator
    {

        /// <summary>
        /// The company name of the registered Economic Operator.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "companyName")]
        public string CompanyName { get; set; }

        /// <summary>
        /// The first line of the registered Economic Operator's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "addressLine1")]
        public string AddressLine1 { get; set; }

        /// <summary>
        /// The second line, if any, of the registered Economic Operator's street address. This field is not always used, but can be used for 'Suite Number' or 'Apt Number'.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "addressLine2")]
        public string AddressLine2 { get; set; }

        /// <summary>
        /// The city of the registered Economic Operator's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "city")]
        public string City { get; set; }

        /// <summary>
        /// The state or province of the registered Economic Operator's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "stateOrProvince")]
        public string StateOrProvince { get; set; }

        /// <summary>
        /// The postal code of the registered Economic Operator's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "postalCode")]
        public string PostalCode { get; set; }

        /// <summary>
        /// The two-letter <a href="https://www.iso.org/iso-3166-country-codes.html " target="_blank">ISO 3166</a> standard abbreviation of the country of the registered Economic Operator's address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "country")]
        public string Country { get; set; }

        /// <summary>
        /// The registered Economic Operator's business phone number.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "phone")]
        public string Phone { get; set; }

        /// <summary>
        /// The registered Economic Operator's business email address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "email")]
        public string Email { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the details of a shipping provider.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ShippingOption
    {

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "additionalShippingCostPerUnit")]
        public ConvertedAmount AdditionalShippingCostPerUnit { get; set; }

        /// <summary>
        /// The deadline date that the item must be purchased by in order to be received by the buyer within the delivery window (<b> maxEstimatedDeliveryDate</b> and  <b> minEstimatedDeliveryDate</b> fields). This field is returned only for items that are eligible for 'Same Day Handling'. For these items, the value of this field is what is displayed in the <b> Delivery</b> line on the View Item page.  <br><br>This value is returned in UTC format (yyyy-MM-ddThh:mm:ss.sssZ), which you can convert into the local time of the buyer.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "cutOffDateUsedForEstimate")]
        public string CutOffDateUsedForEstimate { get; set; }

        /// <summary>
        /// If the item is being shipped by the eBay <a href="https://pages.ebay.com/seller-center/shipping/global-shipping-program.html ">Global Shipping program</a>, this field returns <code>GLOBAL_SHIPPING</code>.<br><br>If the item is being shipped using the eBay International Shipping program, this field returns <code>INTERNATIONAL_SHIPPING</code>. <br><br>Otherwise, this field is null. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:FulfilledThroughEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "fulfilledThrough")]
        public string FulfilledThrough { get; set; }

        /// <summary>
        /// Although this field is still returned, it can be ignored since eBay Guaranteed Delivery is no longer a supported feature on any marketplace. This field may get removed from the schema in the future.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "guaranteedDelivery")]
        public System.Nullable<System.Boolean> GuaranteedDelivery { get; set; }

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "importCharges")]
        public ConvertedAmount ImportCharges { get; set; }

        /// <summary>
        /// The end date of the delivery window (latest projected delivery date).  This value is returned in UTC format (yyyy-MM-ddThh:mm:ss.sssZ), which you can convert into the local time of the buyer. <br> <br> <span class="tablenote"> <b> Note: </b> For the best accuracy, always include the location of where the item is be shipped in the <code> contextualLocation</code> values of the <a href="/api-docs/buy/static/api-browse.html#Headers"> <code>X-EBAY-C-ENDUSERCTX</code></a> request header.</span> 
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "maxEstimatedDeliveryDate")]
        public string MaxEstimatedDeliveryDate { get; set; }

        /// <summary>
        /// The start date of the delivery window (earliest projected delivery date). This value is returned in UTC format (yyyy-MM-ddThh:mm:ss.sssZ), which you can convert into the local time of the buyer. <br> <br><span class="tablenote"> <b> Note: </b> For the best accuracy, always include the location of where the item is be shipped in the <code> contextualLocation</code> values of the <a href="/api-docs/buy/static/api-browse.html#Headers"> <code>X-EBAY-C-ENDUSERCTX</code></a> request header.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "minEstimatedDeliveryDate")]
        public string MinEstimatedDeliveryDate { get; set; }

        /// <summary>
        /// The number of items used when calculating the estimation information.<br><br>This field will reflect the value input in the <b>quantity_for_shipping_estimate</b> query parameter.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "quantityUsedForEstimate")]
        public System.Nullable<System.Int32> QuantityUsedForEstimate { get; set; }

        /// <summary>
        /// The name of the shipping provider, such as FedEx, or USPS.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "shippingCarrierCode")]
        public string ShippingCarrierCode { get; set; }

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "shippingCost")]
        public ConvertedAmount ShippingCost { get; set; }

        /// <summary>
        /// Indicates the class of the shipping cost. <br><br><b> Valid Values: </b> FIXED or CALCULATED <br><br>Code so that your app gracefully handles any future changes to this list. 
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "shippingCostType")]
        public string ShippingCostType { get; set; }

        /// <summary>
        /// The type of shipping service. For example, USPS First Class.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "shippingServiceCode")]
        public string ShippingServiceCode { get; set; }

        /// <summary>
        /// The type that defines the fields for the country and postal code of where an item is to be shipped.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "shipToLocationUsedForEstimate")]
        public ShipToLocation ShipToLocationUsedForEstimate { get; set; }

        /// <summary>
        /// Any trademark symbol, such as &#8482; or &reg;, that needs to be shown in superscript next to the shipping service name.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "trademarkSymbol")]
        public string TrademarkSymbol { get; set; }

        /// <summary>
        /// The type of a shipping option, such as EXPEDITED, ONE_DAY, STANDARD, ECONOMY, PICKUP, etc.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "type")]
        public string Type { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the country and postal code of where an item is to be shipped.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ShipToLocation
    {

        /// <summary>
        /// The two-letter <a href="https://www.iso.org/iso-3166-country-codes.html " target="_blank">ISO 3166</a> standard of the country for where the item is to be shipped. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/ba:CountryCodeEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "country")]
        public string Country { get; set; }

        /// <summary>
        /// The zip code (postal code) for where the item is to be shipped.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "postalCode")]
        public string PostalCode { get; set; }
    }

    /// <summary>
    /// The type that defines the fields that include and exclude geographic regions affecting where the item can be shipped. The seller defines these regions when listing the item.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ShipToLocations
    {

        /// <summary>
        /// An array of containers that express the large geographical regions, countries, state/provinces, or special locations within a country where the seller is not willing to ship to.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "regionExcluded")]
        public ShipToRegion[] RegionExcluded { get; set; }

        /// <summary>
        /// An array of containers that express the large geographical regions, countries, or state/provinces within a country where the seller is willing to ship to. Prospective buyers must look at the shipping regions under this container, as well as the shipping regions that are under the <b>regionExcluded</b> to see where the seller is willing to ship items. Sellers can specify that they ship 'Worldwide', but then add several large geographical regions (e.g. Asia, Oceania, Middle East) to the exclusion list, or sellers can specify that they ship to Europe and Africa, but then add several individual countries to the exclusion list.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "regionIncluded")]
        public ShipToRegion[] RegionIncluded { get; set; }
    }

    /// <summary>
    /// This type is used provide details about included and excluded shipping regions.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ShipToRegion
    {

        /// <summary>
        /// The unique identifier of the shipping region. The value returned here is dependent on the corresponding <b>regionType</b> value. The <b>regionId</b> value for a region does not vary based on the eBay marketplace. However, the corresponding <b>regionName</b> value for a region is a localized, text-based description of the shipping region. <br><br> If the <b>regionType</b> value is <code>WORLDWIDE</code>, the <b>regionId</b> value will also be <code>WORLDWIDE</code>.<br><br> If the <b>regionType</b> value is <code>WORLD_REGION</code>, the <b>regionId</b> value will be one of the following: <code>AFRICA</code>, <code>AMERICAS</code>, <code>ASIA</code>, <code>AUSTRALIA</code>, <code>CENTRAL_AMERICA_AND_CARIBBEAN</code>, <code>EUROPE</code>, <code>EUROPEAN_UNION</code>, <code>GREATER_CHINA</code>, <code>MIDDLE_EAST</code>, <code>NORTH_AMERICA</code>, <code>OCEANIA</code>, <code>SOUTH_AMERICA</code>, <code>SOUTHEAST_ASIA</code> or <code>CHANNEL_ISLANDS</code>.<br><br>If the <b>regionType</b> value is <code>COUNTRY</code>, the <b>regionId</b> value will be the two-letter code for the country, as defined in the <a href="https://www.iso.org/iso-3166-country-codes.html " target="_blank">ISO 3166</a> standard.<br><br>If the <b>regionType</b> value is <code>STATE_OR_PROVINCE</code>, the <b>regionId</b> value will either be the two-letter code for US states and DC (as defined on this <a href="https://www.ssa.gov/international/coc-docs/states.html " target="_blank">Social Security Administration</a> page), or the two-letter code for Canadian provinces (as defined by this <a href="https://www.canadapost.ca/tools/pg/manual/PGaddress-e.asp?ecid=murl10006450#1442131 " target="_blank">Canada Post</a> page).<br><br>If the <b>regionType</b> value is <code>COUNTRY_REGION</code>, the <b>regionId</b> value may be one of following: <code>_AH</code> (if a seller is not willing to ship to Alaska/Hawaii), <code>_PR</code> (if the seller is not willing to ship to US Protectorates), <code>_AP</code> (if seller is not willing to ship to a US Army or Fleet Post Office), and <code>PO_BOX</code> (if the seller is not willing to ship to a Post Office Box).
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "regionId")]
        public string RegionId { get; set; }

        /// <summary>
        /// A localized text string that indicates the name of the shipping region. The value returned here is dependent on the corresponding <b>regionType</b> value. <br><br> If the <b>regionType</b> value is <code>WORLDWIDE</code>, the <b>regionName</b> value will show <code>Worldwide</code>.<br><br> If the <b>regionType</b> value is <code>WORLD_REGION</code>, the <b>regionName</b> value will be a localized text string for one of the following large geographical regions: Africa, Americas, Asia, Australia, Central America and Caribbean, Europe, European Union, Greater China, Middle East, North America, Oceania, South America, Southeast Asia, or Channel Islands.<br><br>If the <b>regionType</b> value is <code>COUNTRY</code>, the <b>regionName</b> value will be a localized text string for any country in the world.<br><br>If the <b>regionType</b> value is <code>STATE_OR_PROVINCE</code>, the <b>regionName</b> value will be a localized text string for any US state or Canadian province. <br><br>If the <b>regionType</b> value is <code>COUNTRY_REGION</code>, the <b>regionName</b> value may be a localized version of one of the following: Alaska/Hawaii, US Protectorates, APO/FPO (Army or Fleet Post Office), or PO BOX.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "regionName")]
        public string RegionName { get; set; }

        /// <summary>
        /// An enumeration value that indicates the level or type of shipping region. <br><br><b> Valid Values: </b> <ul><li><b> COUNTRY_REGION </b> - Indicates the region is a domestic region or special location within a country.</li><li><b> STATE_OR_PROVINCE </b> - Indicates the region is a state or province within a country, such as California or New York in the US, or Ontario or Alberta in Canada.</li><li><b> COUNTRY </b> - Indicates the region is a single country.</li><li><b> WORLD_REGION </b> - Indicates the region is a world region, such as Africa, the Middle East, or Southeast Asia.</li><li><b> WORLDWIDE </b> - Indicates the region is the entire world. This value is only applicable for included shiping regions, and not excluded shipping regions.</li></ul> For more detail on the actual <b>regionName</b>/<b>regionId</b> values that will be returned based on the <b>regionType</b> value, see the <a href="/api-docs/buy/browse/resources/item/methods/getItem#response.shipToLocations.regionExcluded.regionId">regionId</a> and/or <a href="/api-docs/buy/browse/resources/item/methods/getItem#response.shipToLocations.regionExcluded.regionName">regionName</a> field descriptions.<br><br> Code so that your app gracefully handles any future changes to this list. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/ba:RegionTypeEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "regionType")]
        public string RegionType { get; set; }
    }

    /// <summary>
    /// The type that defines the tax fields.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class Taxes
    {

        /// <summary>
        /// This field is only returned if <code>true</code>, and indicates that eBay will collect tax (sales tax, Goods and Services tax, or VAT) for at least one line item in the order, and remit the tax to the taxing authority of the buyer's residence. 
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "ebayCollectAndRemitTax")]
        public System.Nullable<System.Boolean> EbayCollectAndRemitTax { get; set; }

        /// <summary>
        /// This indicates if tax was applied for the cost of the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "includedInPrice")]
        public System.Nullable<System.Boolean> IncludedInPrice { get; set; }

        /// <summary>
        /// This indicates if tax is applied for the shipping cost.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "shippingAndHandlingTaxed")]
        public System.Nullable<System.Boolean> ShippingAndHandlingTaxed { get; set; }

        /// <summary>
        /// The type that defines the fields for the tax jurisdiction details.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "taxJurisdiction")]
        public TaxJurisdiction TaxJurisdiction { get; set; }

        /// <summary>
        /// The percentage of tax.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "taxPercentage")]
        public string TaxPercentage { get; set; }

        /// <summary>
        /// This field indicates the type of tax that may be collected for the item. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:TaxType'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "taxType")]
        public string TaxType { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the tax jurisdiction details.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class TaxJurisdiction
    {

        /// <summary>
        /// This type is used to provide region details for a tax jurisdiction.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "region")]
        public Region Region { get; set; }

        /// <summary>
        /// The identifier of the tax jurisdiction.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "taxJurisdictionId")]
        public string TaxJurisdictionId { get; set; }
    }

    /// <summary>
    /// This type is used to provide region details for a tax jurisdiction.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class Region
    {

        /// <summary>
        /// A localized text string that indicates the name of the region. Taxes are generally charged at the state/province level or at the country level in the case of VAT tax. 
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "regionName")]
        public string RegionName { get; set; }

        /// <summary>
        /// An enumeration value that indicates the type of region for the tax jurisdiction. <br><br><b> Valid Values: </b> <ul><li><b> STATE_OR_PROVINCE </b> - Indicates the region is a state or province within a country, such as California or New York in the US, or Ontario or Alberta in Canada.</li><li><b> COUNTRY </b> - Indicates the region is a single country.</li></ul>  Code so that your app gracefully handles any future changes to this list. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/ba:RegionTypeEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "regionType")]
        public string RegionType { get; set; }
    }

    /// <summary>
    /// A type that defines the pictogram for the type of hazard that a hazardous material represents.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class HazardPictogram
    {

        /// <summary>
        /// The description of the hazard pictogram, such as Flammable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "pictogramDescription")]
        public string PictogramDescription { get; set; }

        /// <summary>
        /// The ID of the hazard pictogram.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "pictogramId")]
        public string PictogramId { get; set; }

        /// <summary>
        /// The URL of the hazard pictogram.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "pictogramUrl")]
        public string PictogramUrl { get; set; }
    }

    /// <summary>
    /// A type that defines the hazard statement for a hazardous material.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class HazardStatement
    {

        /// <summary>
        /// A description of the nature of the hazard, such as whether the material is toxic if swallowed.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "statementDescription")]
        public string StatementDescription { get; set; }

        /// <summary>
        /// The ID of the hazard statement.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "statementId")]
        public string StatementId { get; set; }
    }

    /// <summary>
    /// A type that defines the hazardous materials labels for an item.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class HazardousMaterialsLabels
    {

        /// <summary>
        /// Additional information about the hazardous materials labels.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "additionalInformation")]
        public string AdditionalInformation { get; set; }

        /// <summary>
        /// An array of hazard pictograms that apply to the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "pictograms")]
        public HazardPictogram[] Pictograms { get; set; }

        /// <summary>
        /// The signal word for the hazardous materials label (such as Danger or Warning).
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "signalWord")]
        public string SignalWord { get; set; }

        /// <summary>
        /// The ID of the signal word for the hazardous materials label.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "signalWordId")]
        public string SignalWordId { get; set; }

        /// <summary>
        /// An array of hazard statements for the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "statements")]
        public HazardStatement[] Statements { get; set; }
    }

    /// <summary>
    /// The details of an item that can be purchased.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class Item
    {

        /// <summary>
        /// An array of containers with the URLs for the images that are in addition to the primary image.  The primary image is returned in the <b> image.imageUrl</b> field.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "additionalImages")]
        public Image[] AdditionalImages { get; set; }

        /// <summary>
        /// A list of add-on services that may be selected for the item or that may apply automatically.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "addonServices")]
        public AddonService[] AddonServices { get; set; }

        /// <summary>
        /// This indicates if the item is for  adults only. For more information about adult-only items on eBay, see <a href="https://pages.ebay.com/help/policies/adult-only.html " target="_blank">Adult items policy</a> for sellers and <a href="https://www.ebay.com/help/terms-conditions/default/searching-adult-items?id=4661" target="_blank">Adult-Only items on eBay</a> for buyers.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "adultOnly")]
        public System.Nullable<System.Boolean> AdultOnly { get; set; }

        /// <summary>
        /// (Primary Item Aspect) The age group for which the product is recommended. For example, newborn, infant, toddler, kids, adult, etc. All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "ageGroup")]
        public string AgeGroup { get; set; }

        /// <summary>
        /// A type that identifies whether the item is qualified for the Authenticity Guarantee program.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "authenticityGuarantee")]
        public AuthenticityGuaranteeProgram AuthenticityGuarantee { get; set; }

        /// <summary>
        /// A type that identifies whether the item is from a verified seller.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "authenticityVerification")]
        public AuthenticityVerificationProgram AuthenticityVerification { get; set; }

        /// <summary>
        /// A list of available coupons for the item.<br><br><span class="tablenote"><b>Note:</b> The Browse API only acknowledges item-level coupons. This array will only return coupons linked with an item. Store-level coupons offered by sellers across their entire store will not be returned.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "availableCoupons")]
        public AvailableCoupon[] AvailableCoupons { get; set; }

        /// <summary>
        /// This integer value indicates the total number of bids that have been placed against an auction item. This field is returned only for auction items.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "bidCount")]
        public System.Nullable<System.Int32> BidCount { get; set; }

        /// <summary>
        /// (Primary Item Aspect) The name brand of the item, such as Nike, Apple, etc.  All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "brand")]
        public string Brand { get; set; }

        /// <summary>
        /// A comma separated list of all the purchase options available for the item. The values returned are:<ul><li><code>FIXED_PRICE</code> - Indicates the buyer can purchase the item for a set price using the Buy It Now button.</li><li><code>AUCTION</code> - Indicates the buyer can place a bid for the item. After the first bid is placed, this becomes a live auction item and is the only buying option for this item.</li><li><code>BEST_OFFER</code> - Indicates the buyer can send the seller a price they're willing to pay for the item. The seller can accept, reject, or send a counter offer. For more information on how this works, see <a href="https://www.ebay.com/help/buying/buy-now/making-best-offer?id=4019 ">Making a Best Offer</a>.</li><li><code>CLASSIFIED_AD</code> - Indicates that the final sales transaction is to be completed outside of the eBay environment.</li></ul>Code so that your app gracefully handles any future changes to this list.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "buyingOptions")]
        public string[] BuyingOptions { get; set; }

        /// <summary>
        /// The ID of the leaf category for this item. A leaf category is the lowest level in that category and has no children.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "categoryId")]
        public string CategoryId { get; set; }

        /// <summary>
        /// The IDs of every category in the item path, separated by pipe characters, starting with the top level parent category.<br><br>For example, if an item belongs to the top level category Home and Garden (category ID 11700), followed by Home Improvement (159907), Heating, Cooling and Air (69197), and Thermostats (115947), the field would return the value: <code>11700|159907|69197|115947</code>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "categoryIdPath")]
        public string CategoryIdPath { get; set; }

        /// <summary>
        /// Text that shows the category hierarchy of the item. For example: Computers/Tablets &amp; Networking, Laptops &amp; Netbooks, PC Laptops &amp; Netbooks
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "categoryPath")]
        public string CategoryPath { get; set; }

        /// <summary>
        /// (Primary Item Aspect) Text describing the color of the item.  All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "color")]
        public string Color { get; set; }

        /// <summary>
        /// A short text description for the condition of the item, such as New or Used. For a list of condition names, see <a href="/api-docs/sell/static/metadata/condition-id-values.html " target="_blank">Item Condition IDs and Names</a>.  <br><br>Code so that your app gracefully handles any future changes to this list.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "condition")]
        public string Condition { get; set; }

        /// <summary>
        /// A full text description for the condition of the item. This field elaborates on the value specified in the <b>condition</b> field and provides full details for the condition of the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "conditionDescription")]
        public string ConditionDescription { get; set; }

        /// <summary>
        /// This array is used by the seller to provide additional information about the condition of an item in a structured format. Condition descriptors are name-value attributes that indicate details about a particular condition of an item.<br><br><span class="tablenote"><b>Note:</b> Condition descriptors are currently only available for the following trading card categories:<ul><li>Non-Sport Trading Card Singles</li><li>CCG Individual Cards</li><li>Sports Trading Card Singles</li></ul></span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "conditionDescriptors")]
        public ConditionDescriptor[] ConditionDescriptors { get; set; }

        /// <summary>
        /// The identifier of the condition of the item. For example, 1000 is the identifier for NEW. For a list of condition names and IDs, see <a href="/api-docs/sell/static/metadata/condition-id-values.html " target="_blank">Item Condition IDs and Names</a>. <br><br>Code so that your app gracefully handles any future changes to this list.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "conditionId")]
        public string ConditionId { get; set; }

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "currentBidPrice")]
        public ConvertedAmount CurrentBidPrice { get; set; }

        /// <summary>
        /// The full description of the item that was created by the seller. This can be plain text or rich content and can be very large.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "description")]
        public string Description { get; set; }

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "ecoParticipationFee")]
        public ConvertedAmount EcoParticipationFee { get; set; }

        /// <summary>
        /// This field indicates if the item can be purchased using the Buy <a href="/api-docs/buy/order/resources/methods">Order API</a>. <ul> <li>If the value of this field is <code>true</code>, this indicates that the item can be purchased using the <b> Order API</b>. </li>  <li>If the value of this field is <code>false</code>, this indicates that the item cannot be purchased using the <b> Order API</b> and must be purchased on the eBay site.</li> </ul>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "eligibleForInlineCheckout")]
        public System.Nullable<System.Boolean> EligibleForInlineCheckout { get; set; }

        /// <summary>
        /// This indicates if the item can be purchased using Guest Checkout in the <a href="/api-docs/buy/order/resources/methods">Order API</a>. You can use this flag to exclude items from your inventory that are not eligible for Guest Checkout, such as gift cards.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "enabledForGuestCheckout")]
        public System.Nullable<System.Boolean> EnabledForGuestCheckout { get; set; }

        /// <summary>
        /// This indicates the <a href="https://en.wikipedia.org/wiki/European_Union_energy_label ">European energy efficiency</a> rating (EEK) of the item. This field is returned only if the seller specified the energy efficiency rating. <br><br>The rating is a set of energy efficiency classes from A to G, where 'A' is the most energy efficient and 'G' is the least efficient. This rating helps buyers choose between various models. <br><br>When the manufacturer's specifications for this item are available, the link to this information is returned in the <b> productFicheWebUrl</b> field.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "energyEfficiencyClass")]
        public string EnergyEfficiencyClass { get; set; }

        /// <summary>
        /// An EPID is the eBay product identifier of a product from the eBay product catalog.  This indicates the product in which the item belongs.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "epid")]
        public string Epid { get; set; }

        /// <summary>
        /// The estimated number of this item that are available for purchase. Because the quantity of an item can change several times within a second, it is impossible to return the exact quantity. So instead of returning quantity, the estimated availability of the item is returned.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "estimatedAvailabilities")]
        public EstimatedAvailability[] EstimatedAvailabilities { get; set; }

        /// <summary>
        /// (Primary Item Aspect) The gender for the item. This is used for items that could vary by gender, such as clothing. For example: male, female, or unisex. All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "gender")]
        public string Gender { get; set; }

        /// <summary>
        /// The unique Global Trade Item number of the item as defined by <a href="https://www.gtin.info " target="_blank">https://www.gtin.info</a>. This can be a UPC (Universal Product Code), EAN (European Article Number), or an ISBN (International Standard Book Number) value.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "gtin")]
        public string Gtin { get; set; }

        /// <summary>
        /// A type that defines the hazardous materials labels for an item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "hazardousMaterialsLabels")]
        public HazardousMaterialsLabels HazardousMaterialsLabels { get; set; }

        /// <summary>
        /// Type that defines the details of an image, such as size and image URL. Currently, only <code>imageUrl</code> is populated. The <code>height</code> and <code>width</code> are reserved for future use.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "image")]
        public Image Image { get; set; }

        /// <summary>
        /// A value of <code>true</code> indicates that the seller requires immediate payment from the buyer when purchasing an item.<br><br><span class="tablenote"><b>Note:</b> It is possible for this field to be set to <code>true</code>, but not apply in some scenarios. For example, immediate payment is not applicable for auction listings that have a winning bidder, for buyers purchases that involve the Best Offer feature, or for offline transactions.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "immediatePay")]
        public System.Nullable<System.Boolean> ImmediatePay { get; set; }

        /// <summary>
        /// The ePID (eBay Product ID of a product from the eBay product catalog) for the item, which has been programmatically determined by eBay using the item's title, aspects, and other data. <br><br>If the seller provided an ePID for the item, the seller's value is returned in the <b> epid</b> field. <br><br><span class="tablenote"><b> Note: </b> This field is returned only for authorized Partners.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "inferredEpid")]
        public string InferredEpid { get; set; }

        /// <summary>
        /// The URL to the View Item page of the item which includes the affiliate tracking ID.<br><br><span class="tablenote"><b>Note:</b> In order to receive commissions on sales, eBay Partner Network affiliates must use this URL to forward buyers to the listing on the eBay marketplace.</span><br>The <b>itemAffiliateWebUrl</b> is only returned if:<ul><li>The marketplace through which the item is being viewed is part of the eBay Partner Network. Currently Singapore (<code>EBAY_SG</code>) is <b>not</b> supported.<br><br>For additional information, refer to <a href="https://partnerhelp.ebay.com/helpcenter/s/article/countries-available-as-a-program-in-EPN?language=en_US " target="_blank">eBay Partner Network</a>.</li><li>The seller enables affiliate tracking for the item by including the <code><a href="/api-docs/buy/static/api-browse.html#Headers">X-EBAY-C-ENDUSERCTX</a></code> request header in the method.</li></ul>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemAffiliateWebUrl")]
        public string ItemAffiliateWebUrl { get; set; }

        /// <summary>
        /// A timestamp that indicates the date and time an item listing was created.<br><br>This value is returned in UTC format (yyyy-MM-ddThh:mm:ss.sssZ), which can be converted into the local time of the buyer.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemCreationDate")]
        public string ItemCreationDate { get; set; }

        /// <summary>
        /// A timestamp that indicates the date and time an auction listing will end.<br><br>If a fixed-price listing has ended, this field indicates the date and time the listing ended.<br><br>This value is returned in UTC format (<code>yyyy-MM-ddThh:mm:ss.sssZ</code>), which can be converted into the local time of the buyer.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemEndDate")]
        public string ItemEndDate { get; set; }

        /// <summary>
        /// The unique RESTful identifier of the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemId")]
        public string ItemId { get; set; }

        /// <summary>
        /// The type that defines the fields for an address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemLocation")]
        public Address ItemLocation { get; set; }

        /// <summary>
        /// The URL of the View Item page of the item. This enables you to include a "Report Item on eBay" link that takes the buyer to the View Item page on eBay. From there they can report any issues regarding this item to eBay.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemWebUrl")]
        public string ItemWebUrl { get; set; }

        /// <summary>
        /// The unique identifier of the eBay listing that contains the item. This is the traditional/legacy ID that is often seen in the URL of the listing View Item page.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "legacyItemId")]
        public string LegacyItemId { get; set; }

        /// <summary>
        /// The ID of the eBay marketplace where the item is listed. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/ba:MarketplaceIdEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "listingMarketplaceId")]
        public string ListingMarketplaceId { get; set; }

        /// <summary>
        /// An array of containers that show the complete list of the aspect name/value pairs that describe the variation of the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "localizedAspects")]
        public TypedNameValue[] LocalizedAspects { get; set; }

        /// <summary>
        /// The number of items in a lot. In other words, a lot size is the number of items that are being sold together.  <br><br>A lot is a set of two or more items included in a single listing that must be purchased together in a single order line item. All the items in the lot are the same but there can be multiple items in a single lot,  such as the package of batteries shown in the example below.   <br><br><table border="1"> <tr> <tr>  <th>Item</th>  <th>Lot Definition</th> <th>Lot Size</th></tr>  <tr>  <td>A package of 24 AA batteries</td>  <td>A box of 10 packages</td>  <td>10  </td> </tr>  <tr>  <td>A P235/75-15 Goodyear tire </td>  <td>4 tires  </td>  <td>4  </td> </tr> <tr> <td>Fashion Jewelry Rings  </td> <td>Package of 100 assorted rings  </td> <td>100 </td> </tr></table>  <br><br><span class="tablenote"><b>Note: </b>  Lots are not supported in all categories.  </span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "lotSize")]
        public System.Nullable<System.Int32> LotSize { get; set; }

        /// <summary>
        /// This type is used to provide contact information for the manufacturer of the product.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "manufacturer")]
        public CompanyAddress Manufacturer { get; set; }

        /// <summary>
        /// The type that defines the fields that describe a seller discount.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "marketingPrice")]
        public MarketingPrice MarketingPrice { get; set; }

        /// <summary>
        /// (Primary Item Aspect) Text describing what the item is made of. For example, silk. All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "material")]
        public string Material { get; set; }

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "minimumPriceToBid")]
        public ConvertedAmount MinimumPriceToBid { get; set; }

        /// <summary>
        /// The manufacturer's part number, which is a unique number that identifies a specific product. To identify the product, this is always used along with brand.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "mpn")]
        public string Mpn { get; set; }

        /// <summary>
        /// (Primary Item Aspect) Text describing the pattern used on the item. For example, paisley. All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "pattern")]
        public string Pattern { get; set; }

        /// <summary>
        /// The payment methods for the item, including the payment method types, brands, and instructions for the buyer.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "paymentMethods")]
        public PaymentMethod[] PaymentMethods { get; set; }

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "price")]
        public ConvertedAmount Price { get; set; }

        /// <summary>
        /// Indicates when in the buying flow the item's price can appear for minimum advertised price (MAP) items, which is the lowest price a retailer can advertise/show for this item. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:PriceDisplayConditionEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "priceDisplayCondition")]
        public string PriceDisplayCondition { get; set; }

        /// <summary>
        /// The type that defines the fields for the details of each item in an item group. An item group is  an item that has various aspect differences, such as color, size, storage capacity, etc. When an item group is created, one of the item variations, such as the red shirt size L, is chosen as the "parent". All the other items in the group are the children, such as the blue shirt size L, red shirt size M, etc. <br><br><span class="tablenote"><b> Note: </b> This container is returned only if the <b> item_id</b> in the request is an item group (parent ID of an item with variations).</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "primaryItemGroup")]
        public ItemGroupSummary PrimaryItemGroup { get; set; }

        /// <summary>
        /// The type that defines the fields for the rating of a product review.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "primaryProductReviewRating")]
        public ReviewRating PrimaryProductReviewRating { get; set; }

        /// <summary>
        /// This field is returned as <code>true</code> if the listing is part of a Promoted Listing campaign. Promoted Listings are available to Above Standard and Top Rated sellers with recent sales activity.<br><br>For more information, see <a href="https://pages.ebay.com/seller-center/listing-and-marketing/promoted-listings.html " target="_blank">Promoted Listings</a>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "priorityListing")]
        public System.Nullable<System.Boolean> PriorityListing { get; set; }

        /// <summary>
        /// The type that defines the fields for the product information of the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "product")]
        public Product Product { get; set; }

        /// <summary>
        /// The URL of a page containing the manufacturer's specification of this item, which helps buyers make a purchasing decision. This information is available only for items that include the European energy efficiency rating (EEK) but is not available for <em> all</em> items with an EEK rating and is returned only if this information is available. The EEK rating of the item is returned in the <b> energyEfficiencyClass</b> field.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "productFicheWebUrl")]
        public string ProductFicheWebUrl { get; set; }

        /// <summary>
        /// This type contains seller provided product safety pictograms and statements for the listing.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "productSafetyLabels")]
        public ProductSafetyLabels ProductSafetyLabels { get; set; }

        /// <summary>
        /// An array of the qualified programs available for the item, or for the item group when returned for the <b>getItemsByItemGroup</b> method, such as EBAY_PLUS, AUTHENTICITY_GUARANTEE, and AUTHENTICITY_VERIFICATION.<br><br><span class="tablenote"><b>Note: </b>The <code>AUTHENTICITY_GUARANTEE</code> value being returned by the <b>getItemsByItemGroup</b> method indicates that at least one item in the item group supports this program, but doesn't guarantee that the program is available to all items in the item group. To verify if the Authenticity Program is indeed available for the item that you are interested in, grab the <b>items.itemId</b> value for that item and use the <b>getItem</b> method. This method will return specific details on that particular item, including whether or not the Authenticity Guarantee Program is available for the item. Look for the <b>qualifiedPrograms</b> array and <b>authenticityGuarantee</b> container in the <b>getItem</b> response for this information.</span><br><br>eBay Plus is a premium account option for buyers, which provides benefits such as fast free domestic shipping and free returns on selected items. Top-Rated eBay sellers must opt in to eBay Plus to be able to offer the program on qualifying listings. Sellers must commit to next-day delivery of those items.<br><br><span class="tablenote"><b>Note: </b> eBay Plus is available only to buyers in Germany, Austria, and Australia marketplaces.</span><br><br>The eBay <a href="https://pages.ebay.com/authenticity-guarantee/ " target="_blank">Authenticity Guarantee</a> program enables third-party authenticators to perform authentication verification inspections on items such as watches and sneakers.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "qualifiedPrograms")]
        public string[] QualifiedPrograms { get; set; }

        /// <summary>
        /// The maximum number for a specific item that one buyer can purchase.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "quantityLimitPerBuyer")]
        public System.Nullable<System.Int32> QuantityLimitPerBuyer { get; set; }

        /// <summary>
        /// A score that describes how easy it is to repair the product. Score values range from 0.1 (hardest to repair) to 10.0 (easiest), always including a single decimal place.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "repairScore")]
        public string RepairScore { get; set; }

        /// <summary>
        /// This indicates if the reserve price of the item has been met. A reserve price is set by the seller and is the minimum amount the seller is willing to sell the item for. <p>If the highest bid is not equal to or higher than the reserve price when the auction ends, the listing ends and the item is not sold.</p> <p><b> Note: </b>This is returned only for auctions that have a reserve price.</p>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "reservePriceMet")]
        public System.Nullable<System.Boolean> ReservePriceMet { get; set; }

        /// <summary>
        /// This array provides information about one or more EU-based Responsible Persons or entities associated with the listing.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "responsiblePersons")]
        public ResponsiblePerson[] ResponsiblePersons { get; set; }

        /// <summary>
        /// The type that defines the fields for the seller's return policy.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "returnTerms")]
        public ItemReturnTerms ReturnTerms { get; set; }

        /// <summary>
        /// The type that defines the fields for basic and detailed information about the seller of the item returned by the <b> item</b> resource.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "seller")]
        public SellerDetail Seller { get; set; }

        /// <summary>
        /// A list of the custom policies that are applied to a listing.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "sellerCustomPolicies")]
        public SellerCustomPolicy[] SellerCustomPolicies { get; set; }

        /// <summary>
        /// An identifier generated/incremented when a seller revises the item. There are two types of item revisions: <ul><li>Seller changes, such as changing the title</li>  <li>eBay system changes, such as changing the quantity when an item is purchased</li></ul> This ID is changed <em> only</em> when the seller makes a change to the item. This means you cannot use this value to determine if the quantity has changed.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "sellerItemRevision")]
        public string SellerItemRevision { get; set; }

        /// <summary>
        /// An array of shipping options containers that have the details about cost, carrier, etc. of one shipping option.<br><br><span class="tablenote"><b>Note:</b> For items with calculated shipping, this array is only returned if the <b>X-EBAY-C-ENDUSERCTX</b> header is supplied.</span> 
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "shippingOptions")]
        public ShippingOption[] ShippingOptions { get; set; }

        /// <summary>
        /// The type that defines the fields that include and exclude geographic regions affecting where the item can be shipped. The seller defines these regions when listing the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "shipToLocations")]
        public ShipToLocations ShipToLocations { get; set; }

        /// <summary>
        /// This text string is derived from the item condition and the item aspects (such as size, color, capacity, model, brand, etc.).
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "shortDescription")]
        public string ShortDescription { get; set; }

        /// <summary>
        /// (Primary Item Aspect) The size of the item. For example, '7' for a size 7 shoe. All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "size")]
        public string Size { get; set; }

        /// <summary>
        /// (Primary Item Aspect) The sizing system of the country.  All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container. <br><br><b> Valid Values: </b> <br>AU (Australia),  <br>BR (Brazil), <br>CN (China),  <br>DE (Germany),  <br>EU (European Union),  <br> FR (France), <br> IT (Italy),  <br>JP (Japan), <br>MX (Mexico),  <br>US (USA), <br> UK (United Kingdom) <br><br>Code so that your app gracefully handles any future changes to this list. 
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "sizeSystem")]
        public string SizeSystem { get; set; }

        /// <summary>
        /// (Primary Item Aspect) Text describing a size group in which the item would be included, such as regular, petite, plus, big-and-tall or maternity. All the item aspects, including this aspect, are returned in the <b> localizedAspects</b> container.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "sizeType")]
        public string SizeType { get; set; }

        /// <summary>
        /// A subtitle is optional and allows the seller to provide more information about the product, possibly including keywords that may assist with search results.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "subtitle")]
        public string Subtitle { get; set; }

        /// <summary>
        /// The container for the tax information for the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "taxes")]
        public Taxes[] Taxes { get; set; }

        /// <summary>
        /// The seller-created title of the item. <br><br><b> Maximum Length: </b> 80 characters
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "title")]
        public string Title { get; set; }

        /// <summary>
        /// This indicates if the item a top-rated plus item. There are three benefits of a top-rated plus item: a  minimum 30-day money-back return policy, shipping the items in 1 business day with tracking provided, and the added comfort of knowing this item is from experienced sellers with the highest buyer ratings. See the <a href="https://pages.ebay.com/topratedplus/index.html " target="_blank">Top Rated Plus Items </a> and <a href="https://pages.ebay.com/help/sell/top-rated.html " target="_blank">Becoming a Top Rated Seller and qualifying for Top Rated Plus</a> help topics for more information.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "topRatedBuyingExperience")]
        public System.Nullable<System.Boolean> TopRatedBuyingExperience { get; set; }

        /// <summary>
        /// The URL to the image that shows the information on the tyre label.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "tyreLabelImageUrl")]
        public string TyreLabelImageUrl { get; set; }

        /// <summary>
        /// This integer value indicates the number of different eBay users who have placed one or more bids on an auction item. This field is only applicable to auction items.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "uniqueBidderCount")]
        public System.Nullable<System.Int32> UniqueBidderCount { get; set; }

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "unitPrice")]
        public ConvertedAmount UnitPrice { get; set; }

        /// <summary>
        /// The designation, such as size, weight, volume, count, etc., that was used to specify the quantity of the item.  This helps buyers compare prices. <br><br>For example, the following tells the buyer that the item is 7.99 per 100 grams. <br><br><code>"unitPricingMeasure": "100g",<br> "unitPrice": {<br>&nbsp;&nbsp;"value": "7.99",<br>&nbsp;&nbsp;"currency": "GBP"</code>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "unitPricingMeasure")]
        public string UnitPricingMeasure { get; set; }

        /// <summary>
        /// An array of warning messages. These types of errors do not prevent the method from executing but should be checked.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "warnings")]
        public Error[] Warnings { get; set; }

        /// <summary>
        /// The number of users that have added the item to their watch list.<br><br><span class="tablenote"> <strong>Note:</strong> This field is restricted to applications that have been granted permission to access this feature. You must submit an <a href="https://developer.ebay.com/my/support/tickets?tab=app-check ">App Check ticket</a> to request this access. In the App Check form, add a note to the <b>Application Title/Summary</b> and/or <b>Application Details</b> fields that you want access to Watch Count data in the Browse API.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "watchCount")]
        public System.Nullable<System.Int32> WatchCount { get; set; }
    }

    /// <summary>
    /// This type contains seller provided product safety pictograms and statements for the listing.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ProductSafetyLabels
    {

        /// <summary>
        /// An array of seller provided comma-separated string values that provides identifier, URL, and description for one or more pictograms associated with the listing.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "pictograms")]
        public ProductSafetyLabelPictogram[] Pictograms { get; set; }

        /// <summary>
        /// An array of seller provided comma-separated string values that provide identifier and description for one or more product safety statements associated with the listing.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "statements")]
        public ProductSafetyLabelStatement[] Statements { get; set; }
    }

    /// <summary>
    /// This type is used to provide product safety pictogram(s) for the listing.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ProductSafetyLabelPictogram
    {

        /// <summary>
        /// The description of the safety label pictogram.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "pictogramDescription")]
        public string PictogramDescription { get; set; }

        /// <summary>
        /// The identifier of the safety label pictogram.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "pictogramId")]
        public string PictogramId { get; set; }

        /// <summary>
        /// The URL of the safety label pictogram.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "pictogramUrl")]
        public string PictogramUrl { get; set; }
    }

    /// <summary>
    /// This type is used to describe the seller provided product safety label statement.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ProductSafetyLabelStatement
    {

        /// <summary>
        /// A description of the nature of the product safety label statement.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "statementDescription")]
        public string StatementDescription { get; set; }

        /// <summary>
        /// The identifier of the product safety label statement.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "statementId")]
        public string StatementId { get; set; }
    }

    /// <summary>
    /// This type provides information, such as name and contact details, for an EU-based Responsible Person or entity, associated with the product.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ResponsiblePerson
    {

        /// <summary>
        /// The first line of the Responsible Person's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "addressLine1")]
        public string AddressLine1 { get; set; }

        /// <summary>
        /// The second line of the Responsible Person's address. This field is not always used, but can be used for secondary address information such as 'Suite Number' or 'Apt Number'.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "addressLine2")]
        public string AddressLine2 { get; set; }

        /// <summary>
        /// The city of the Responsible Person's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "city")]
        public string City { get; set; }

        /// <summary>
        /// The name of the Responsible Person or entity.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "companyName")]
        public string CompanyName { get; set; }

        /// <summary>
        /// The contact URL of the Responsible Person or entity.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "contactUrl")]
        public string ContactUrl { get; set; }

        /// <summary>
        /// The two-letter <a href="https://www.iso.org/iso-3166-country-codes.html " target="_blank">ISO 3166</a> standard of the country of the address. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/ba:CountryCodeEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "country")]
        public string Country { get; set; }

        /// <summary>
        /// The country name of the Responsible Person's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "countryName")]
        public string CountryName { get; set; }

        /// <summary>
        /// The county of the Responsible Person's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "county")]
        public string County { get; set; }

        /// <summary>
        /// The email of the Responsible Person's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "email")]
        public string Email { get; set; }

        /// <summary>
        /// The phone number of the Responsible Person's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "phone")]
        public string Phone { get; set; }

        /// <summary>
        /// The postal code of the Responsible Person's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "postalCode")]
        public string PostalCode { get; set; }

        /// <summary>
        /// The state or province of the Responsible Person's street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "stateOrProvince")]
        public string StateOrProvince { get; set; }

        /// <summary>
        /// The type(s) associated with the Responsible Person or entity.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "types")]
        public string[] Types { get; set; }
    }

    /// <summary>
    /// The container for custom policies that apply to a listed item.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class SellerCustomPolicy
    {

        /// <summary>
        /// The seller-defined description of the policy.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "description")]
        public string Description { get; set; }

        /// <summary>
        /// The seller-defined label for an individual custom policy.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "label")]
        public string Label { get; set; }

        /// <summary>
        /// The type of custom policy, such as PRODUCT_COMPLIANCE or TAKE_BACK. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:SellerCustomPolicyTypeEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "type")]
        public string Type { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the item details.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ItemGroup
    {

        /// <summary>
        /// An array of containers for a description and the item IDs of all the items that have this exact description. Often the item variations within an item group all have the same description. Instead of repeating this description in the item details of each item, a description that is shared by at least one other item is returned in this container. If the description is unique, it is returned in the <b> items.description</b> field.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "commonDescriptions")]
        public CommonDescriptions[] CommonDescriptions { get; set; }

        /// <summary>
        /// An array of containers for all the item variation details, excluding the description.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "items")]
        public Item[] Items { get; set; }

        /// <summary>
        /// An array of warning messages. These types of errors do not prevent the method from executing but should be checked.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "warnings")]
        public Error[] Warnings { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the location of an item, such as information typically used for an address, including postal code, county, state/province, street address, city, and country (2-digit ISO code).
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ItemLocationImpl
    {

        /// <summary>
        /// The first line of the street address.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "addressLine1")]
        public string AddressLine1 { get; set; }

        /// <summary>
        /// The second line of the street address. This field may contain such values as an apartment or suite number.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "addressLine2")]
        public string AddressLine2 { get; set; }

        /// <summary>
        /// The city in which the item is located.<br><br><b>Restriction:</b> This field is populated in the <code>search</code> method response <i>only</i> when <code>fieldgroups</code> = <code>EXTENDED</code>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "city")]
        public string City { get; set; }

        /// <summary>
        /// The two-letter <a href="https://www.iso.org/iso-3166-country-codes.html " target="_blank">ISO 3166</a> standard code that indicates the country in which the item is located. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/ba:CountryCodeEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "country")]
        public string Country { get; set; }

        /// <summary>
        /// The county in which the item is located.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "county")]
        public string County { get; set; }

        /// <summary>
        /// The postal code (or zip code in US) where the item is located. Sellers set a postal code for items when they are listed. The postal code is used for calculating proximity searches. It is anonymized when returned in <code>itemLocation.postalCode</code> via the API.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "postalCode")]
        public string PostalCode { get; set; }

        /// <summary>
        /// The state or province in which the item is located.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "stateOrProvince")]
        public string StateOrProvince { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the details of a specific item.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ItemSummary
    {

        /// <summary>
        /// An array of containers with the URLs for the images that are in addition to the primary image. The primary image is returned in the <code>image.imageUrl</code> field.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "additionalImages")]
        public Image[] AdditionalImages { get; set; }

        /// <summary>
        /// This indicates if the item is for adults only. For more information about adult-only items on eBay, refer to the <a href="https://www.ebay.com/help/policies/prohibited-restricted-items/adult-items-policy?id=4278 " target="_blank">Adult items policy</a>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "adultOnly")]
        public System.Nullable<System.Boolean> AdultOnly { get; set; }

        /// <summary>
        /// This boolean attribute indicates if coupons are available for the item.<br><br><span class="tablenote"><b>Note:</b> The Browse API only acknowledges item-level coupons. This field will only be returned as true if a coupon is linked with an item. It does not recognize store-level coupons offered by sellers across their entire store.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "availableCoupons")]
        public System.Nullable<System.Boolean> AvailableCoupons { get; set; }

        /// <summary>
        /// This integer value indicates the total number of bids that have been placed for an auction item. This field is only returned for auction items.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "bidCount")]
        public System.Nullable<System.Int32> BidCount { get; set; }

        /// <summary>
        /// A comma separated list of all the purchase options available for the item.<br><br><b>Values Returned:</b><ul><li><code>FIXED_PRICE</code><br>Indicates the buyer can purchase the item for a set price using the <i>Buy It Now</i> button.</li><li><code>AUCTION</code><br>Indicates the buyer can place a bid for the item. After the first bid is placed, this becomes a live auction item and is the only buying option for this item.</li><li><code>BEST_OFFER</code><br>Items where the buyer can send the seller a price they are willing to pay for the item. The seller can accept, reject, or send a counter offer. For additional information about Best Offer, refer to <a href="https://www.ebay.com/help/selling/listings/selling-buy-now/adding-best-offer-listing?id=4144 " target="_blank">Adding Best Offer to your listing and sending offers to buyers</a>.</li><li><code>CLASSIFIED_AD</code><br>Indicates that the final sales transaction is to be completed outside of the eBay environment.</li></ul>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "buyingOptions")]
        public string[] BuyingOptions { get; set; }

        /// <summary>
        /// This array returns the name and ID of each category associated with the item, including top level, branch, and leaf categories.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "categories")]
        public Category[] Categories { get; set; }

        /// <summary>
        /// This indicates how well an item matches the <code>compatibility_filter</code> product attributes.<br><br><b>Valid Values:</b><ul><li><code>EXACT</code></li><li><code>POSSIBLE</li></ul> For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:CompatibilityMatchEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "compatibilityMatch")]
        public string CompatibilityMatch { get; set; }

        /// <summary>
        /// This container returns only the product attributes that are compatible with the item. These attributes were specified in the <code>compatibility_filter</code> in the request. This means that if you passed in 5 attributes and only 4 are compatible, only those 4 are returned. If none of the attributes are compatible, this container is not returned.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "compatibilityProperties")]
        public CompatibilityProperty[] CompatibilityProperties { get; set; }

        /// <summary>
        /// The text describing the condition of the item, such as <b>New</b> or <b>Used</b>. For a list of condition names, refer to <a href="/api-docs/sell/static/metadata/condition-id-values.html " target="_blank">Item Condition IDs and Names</a>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "condition")]
        public string Condition { get; set; }

        /// <summary>
        /// The identifier of the condition of the item. For example, <code>1000</code> is the identifier for <code>NEW</code>. For a list of condition names and IDs, refer to <a href="/api-docs/sell/static/metadata/condition-id-values.html " target="_blank">Item Condition IDs and Names</a>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "conditionId")]
        public string ConditionId { get; set; }

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "currentBidPrice")]
        public ConvertedAmount CurrentBidPrice { get; set; }

        /// <summary>
        /// The type that defines the fields for the distance between the item location and the buyer's location. 
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "distanceFromPickupLocation")]
        public TargetLocation DistanceFromPickupLocation { get; set; }

        /// <summary>
        /// This indicates the <a href="https://en.wikipedia.org/wiki/European_Union_energy_label " target="_blank">European energy efficiency</a> rating (EEK) of the item. Energy efficiency ratings apply to products listed by commercial vendors in electronics categories only. <br><br>Currently, this field is only applicable for the Germany site, and is returned only if the seller specifies the energy efficiency rating through item specifics at listing time. Rating values include <code>A+++</code>, <code>A++</code>, <code>A+</code>, <code>A</code>, <code>B</code>, <code>C</code>, <code>D</code>, <code>E</code>, <code>F</code>, and <code>G</code>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "energyEfficiencyClass")]
        public string EnergyEfficiencyClass { get; set; }

        /// <summary>
        /// An ePID is the eBay product identifier of a product from the eBay product catalog.  This indicates the product in which the item belongs.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "epid")]
        public string Epid { get; set; }

        /// <summary>
        /// Type that defines the details of an image, such as size and image URL. Currently, only <code>imageUrl</code> is populated. The <code>height</code> and <code>width</code> are reserved for future use.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "image")]
        public Image Image { get; set; }

        /// <summary>
        /// The URL to the View Item page of the item which includes the affiliate tracking ID.<br><br><span class="tablenote"><b>Note:</b> In order to receive commissions on sales, eBay Partner Network affiliates must use this URL to forward buyers to the listing on the eBay marketplace.</span><br>The <code>itemAffiliateWebUrl</code> is returned only if:<ul><li>The marketplace through which the item is being viewed is part of the eBay Partner Network. Currently Singapore (<code>EBAY_SG</code>) is <b>not</b> supported.<br><br>For additional information, refer to <a href="https://partnerhelp.ebay.com/helpcenter/s/article/countries-available-as-a-program-in-EPN?language=en_US " target="_blank">eBay Partner Network</a>.</li><li>The seller enables affiliate tracking for the item by including the <code><a href="/api-docs/buy/static/api-browse.html#Headers">X-EBAY-C-ENDUSERCTX</a></code> request header in the method.</li></ul>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemAffiliateWebUrl")]
        public string ItemAffiliateWebUrl { get; set; }

        /// <summary>
        /// The date and time when the item listing was created. This value is returned in UTC format (yyyy-MM-ddThh:mm:ss.sssZ), which you can convert into the local time of the buyer.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemCreationDate")]
        public string ItemCreationDate { get; set; }

        /// <summary>
        /// A timestamp that indicates the date and time an auction listing will end.<br><br>This value is returned in UTC format (<code>yyyy-MM-ddThh:mm:ss.sssZ</code>), which can be converted into the local time of the buyer.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemEndDate")]
        public string ItemEndDate { get; set; }

        /// <summary>
        /// The HATEOAS reference of the parent page of the item group. An item group is an item that has various aspect differences, such as color, size, storage capacity, etc.<br><br><span class="tablenote"><b>Note:</b> This field is returned only for item groups.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemGroupHref")]
        public string ItemGroupHref { get; set; }

        /// <summary>
        /// The indicates the item group type. An item group is an item that has various aspect differences, such as color, size, storage capacity, etc. <br><br>Currently only the <code>SELLER_DEFINED_VARIATIONS</code> is supported and indicates this is an item group created by the seller.<br><br><span class="tablenote"><b>Note:</b> This field is returned only for item groups.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemGroupType")]
        public string ItemGroupType { get; set; }

        /// <summary>
        /// The URI for the Browse API <a href="/api-docs/buy/browse/resources/item/methods/getItem" target="_blank">getItem</a> method, which can be used to retrieve more details about items in the search results.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemHref")]
        public string ItemHref { get; set; }

        /// <summary>
        /// The unique RESTful identifier of the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemId")]
        public string ItemId { get; set; }

        /// <summary>
        /// The type that defines the fields for the location of an item, such as information typically used for an address, including postal code, county, state/province, street address, city, and country (2-digit ISO code).
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemLocation")]
        public ItemLocationImpl ItemLocation { get; set; }

        /// <summary>
        /// The URL to the View Item page of the item. This enables you to include a "Report Item on eBay" hyperlink that takes the buyer to the View Item page on eBay. From there they can report any issues regarding this item to eBay.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemWebUrl")]
        public string ItemWebUrl { get; set; }

        /// <summary>
        /// The leaf category IDs of the item. When the item belongs to two leaf categories, the ID values are returned in the order primary, secondary.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "leafCategoryIds")]
        public string[] LeafCategoryIds { get; set; }

        /// <summary>
        /// The unique identifier of the eBay listing that contains the item. This is the traditional/legacy ID that is often seen in the URL of the listing View Item page.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "legacyItemId")]
        public string LegacyItemId { get; set; }

        /// <summary>
        /// The ID of the eBay marketplace on which the seller listed the item. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/ba:MarketplaceIdEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "listingMarketplaceId")]
        public string ListingMarketplaceId { get; set; }

        /// <summary>
        /// The type that defines the fields that describe a seller discount.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "marketingPrice")]
        public MarketingPrice MarketingPrice { get; set; }

        /// <summary>
        /// This container returns the local pickup options available to the buyer. This container is returned only if the user is searching for local pickup items and set the local pickup filters in the method request.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "pickupOptions")]
        public PickupOptionSummary[] PickupOptions { get; set; }

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "price")]
        public ConvertedAmount Price { get; set; }

        /// <summary>
        /// Indicates when in the buying flow the item's price can appear for minimum advertised price (MAP) items, which is the lowest price a retailer can advertise/show for this item. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/buy/browse/types/gct:PriceDisplayConditionEnum'>eBay API documentation</a>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "priceDisplayCondition")]
        public string PriceDisplayCondition { get; set; }

        /// <summary>
        /// This field is returned as <code>true</code> if the listing is part of a Promoted Listing campaign. Promoted Listings are available to <b>Above Standard</b> and <b>Top Rated</b> sellers with recent sales activity.<br><br><span class="tablenote"><b>Note:</b> Priority Listing is returned only with a Best Match sort and will not be returned for other sort options.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "priorityListing")]
        public System.Nullable<System.Boolean> PriorityListing { get; set; }

        /// <summary>
        /// An array of the qualified programs available for the item, such as <code>EBAY_PLUS</code>, <code>AUTHENTICITY_GUARANTEE</code>, and <code>AUTHENTICITY_VERIFICATION</code>.<br><br>eBay Plus is a premium account option for buyers, which provides benefits such as fast, free domestic shipping and free returns on selected items. Top-Rated eBay sellers must opt in to eBay Plus to be able to offer the program on qualifying listings. Sellers must commit to next-day delivery of those items.<br><br><span class="tablenote"><b>Note: </b> eBay Plus is available only to buyers in the Germany, Austria, and Australia marketplaces.</span><br><br>The eBay <a href="https://pages.ebay.com/authenticity-guarantee/ " target="_blank">Authenticity Guarantee</a> program enables third-party authenticators to perform authentication verification inspections on items such as watches and sneakers.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "qualifiedPrograms")]
        public string[] QualifiedPrograms { get; set; }

        /// <summary>
        /// The type that defines the fields for basic information about the seller of the item returned by the <code>item_summary</code> resource.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "seller")]
        public Seller Seller { get; set; }

        /// <summary>
        /// This container returns the shipping options available to ship the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "shippingOptions")]
        public ShippingOptionSummary[] ShippingOptions { get; set; }

        /// <summary>
        /// This text string is derived from the item condition and the item aspects (such as size, color, capacity, model, brand, etc.) Sometimes the title does not provide enough information but the description is too big. Surfacing the <code>shortDescription</code> can often provide buyers with the additional information that could help them make a buying decision.<br><br>For example:<pre>"<b>title</b>": "Petrel U42W FPV Drone RC Quadcopter w/HD Camera Live Video One Key Off / Landing",<br>"<b>shortDescription</b>": "1 U42W Quadcopter. Syma X5SW-V3 Wifi FPV RC Drone Quadcopter 2.4Ghz 6-Axis Gyro with Headless Mode. Syma X20 Pocket Drone 2.4Ghz Mini RC Quadcopter Headless Mode Altitude Hold. One Key Take Off / Landing function: allow beginner to easy to fly the drone without any skill.",</pre><br><b>Restriction:</b> This field is returned by the <b>search</b> method only when <code>fieldgroups</code> = <code>EXTENDED</code>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "shortDescription")]
        public string ShortDescription { get; set; }

        /// <summary>
        /// An array of thumbnail images for the item.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "thumbnailImages")]
        public Image[] ThumbnailImages { get; set; }

        /// <summary>
        /// The seller-created title of the item.<br><br><b>Maximum Length:</b> 80 characters
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "title")]
        public string Title { get; set; }

        /// <summary>
        /// This indicates if the item is a top-rated plus item. There are three benefits of a top-rated plus item: a  minimum 30-day money-back return policy; shipping the item in 1 business day with tracking provided; and the added comfort of knowing that this item is from an experienced seller with the highest buyer ratings. For more information, refer to <a href="https://pages.ebay.com/topratedplus/index.html " target="_blank">Look for Top Rated Plus Items</a> and <a href="https://www.ebay.com/help/selling/seller-levels-performance-standards/seller-levels-performance-standards?id=4080 " target="_blank">Seller performance overview</a>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "topRatedBuyingExperience")]
        public System.Nullable<System.Boolean> TopRatedBuyingExperience { get; set; }

        /// <summary>
        /// The URL to the image that shows the information on the tyre label.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "tyreLabelImageUrl")]
        public string TyreLabelImageUrl { get; set; }

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "unitPrice")]
        public ConvertedAmount UnitPrice { get; set; }

        /// <summary>
        /// The designation, such as size, weight, volume, count, etc., that was used to specify the quantity of the item. This helps buyers compare prices.<br><br>For example, the following tells the buyer that the item is 7.99 per 100 grams.<pre>"unitPricingMeasure": "100g",<br> "unitPrice": {<br>&nbsp;&nbsp;"value": "7.99",<br>&nbsp;&nbsp;"currency": "GBP"</pre>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "unitPricingMeasure")]
        public string UnitPricingMeasure { get; set; }

        /// <summary>
        /// The number of users that have added the item to their watch list.<br><br><span class="tablenote"><b>Note:</b> This field is restricted to applications that have been granted permission to access this feature. You must submit an <a href="/my/support/tickets?tab=app-check " target="_blank">App Check ticket</a> to request this access. In the App Check form, add a note to the <b>Application Title/Summary</b> and/or <b>Application Details</b> fields indicating that you want access to Watch Count data in the Browse API.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "watchCount")]
        public System.Nullable<System.Int32> WatchCount { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the distance between the item location and the buyer's location. 
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class TargetLocation
    {

        /// <summary>
        /// This value shows the unit of measurement used to measure the distance between the location of the item and the buyer's location. This value is typically <code> mi</code> or <code> km</code>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "unitOfMeasure")]
        public string UnitOfMeasure { get; set; }

        /// <summary>
        /// This value indicates the distance (measured in the measurement unit in the <b> unitOfMeasure</b>  field) between the item location and the buyer's location.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "value")]
        public string Value { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the local pickup options that are available for the item. It is used by the <code>pickupOptions</code> container.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class PickupOptionSummary
    {

        /// <summary>
        /// This container returns the local pickup options available to the buyer. Possible values are <code>ARRANGED_LOCATION</code> and <code>STORE</code>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "pickupLocationType")]
        public string PickupLocationType { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for basic information about the seller of the item returned by the <code>item_summary</code> resource.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class Seller
    {

        /// <summary>
        /// The percentage of the total positive feedback.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "feedbackPercentage")]
        public string FeedbackPercentage { get; set; }

        /// <summary>
        /// The feedback score of the seller. This value is based on the ratings from eBay members that bought items from this seller.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "feedbackScore")]
        public System.Nullable<System.Int32> FeedbackScore { get; set; }

        /// <summary>
        /// Indicates if the seller is a business or an individual. This is determined when the seller registers with eBay:<ul><li>If they register for a business account, this value will be <code>BUSINESS<code>.</li><li>If they register for a private account, this value will be <code>INDIVIDUAL</code>.</li></ul>This designation is required by the tax laws in some countries.<br><br>This field is returned only on the following sites:<br><br>EBAY_AT, EBAY_BE, EBAY_CH, EBAY_DE, EBAY_ES, EBAY_FR, EBAY_GB, EBAY_IE, EBAY_IT, EBAY_PL<br><br><b>Valid Values:</b> <code>BUSINESS</code> or <code>INDIVIDUAL</code>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "sellerAccountType")]
        public string SellerAccountType { get; set; }

        /// <summary>
        /// The user name created by the seller for use on eBay.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "username")]
        public string Username { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the shipping information.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class ShippingOptionSummary
    {

        /// <summary>
        /// Although this field is still returned, it can be ignored since eBay Guaranteed Delivery is no longer a supported feature on any marketplace. This field may get removed from the schema in the future.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "guaranteedDelivery")]
        public System.Nullable<System.Boolean> GuaranteedDelivery { get; set; }

        /// <summary>
        /// The end date of the delivery window (latest projected delivery date). This value is returned in UTC format (yyyy-MM-ddThh:mm:ss.sssZ), which you can convert into the local time of the buyer.<br><br><span class="tablenote"> <b>Note: </b> For the best accuracy, always include the <code>contextualLocation</code> values in the <a href="/api-docs/buy/static/api-browse.html#Headers" target="_blank"><code>X-EBAY-C-ENDUSERCTX</code></a> request header.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "maxEstimatedDeliveryDate")]
        public string MaxEstimatedDeliveryDate { get; set; }

        /// <summary>
        /// The start date of the delivery window (earliest projected delivery date). This value is returned in UTC format (yyyy-MM-ddThh:mm:ss.sssZ), which you can convert into the local time of the buyer.<br><br><span class="tablenote"><b>Note:</b> For the best accuracy, always include the <code>contextualLocation</code> values in the <a href="/api-docs/buy/static/api-browse.html#Headers" target="_blank"><code>X-EBAY-C-ENDUSERCTX</code></a> request header.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "minEstimatedDeliveryDate")]
        public string MinEstimatedDeliveryDate { get; set; }

        /// <summary>
        /// This type defines the monetary value of an amount. It can provide the amount in both the currency used on the eBay site where an item is being offered and the conversion of that value into another currency, if applicable.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "shippingCost")]
        public ConvertedAmount ShippingCost { get; set; }

        /// <summary>
        /// Indicates the type of shipping used to ship the item. Possible values are <code>FIXED</code> (flat-rate shipping) and <code>CALCULATED</code> (shipping cost calculated based on item and buyer location).
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "shippingCostType")]
        public string ShippingCostType { get; set; }
    }

    /// <summary>
    /// Container for a list of items.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class Items
    {

        /// <summary>
        /// An arraylist of all the items.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "items")]
        public CoreItem[] Items1 { get; set; }

        /// <summary>
        /// The total number of items retrieved.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "total")]
        public System.Nullable<System.Int32> Total { get; set; }

        /// <summary>
        /// An array of warning messages. These types of errors do not prevent the method from executing but should be checked.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "warnings")]
        public Error[] Warnings { get; set; }
    }

    /// <summary>
    /// This type defines the fields for the various refinements of an item. You can use the information in this container to create histograms, which help shoppers choose exactly what they want.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class Refinement
    {

        /// <summary>
        /// An array of containers for the all the aspect refinements.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "aspectDistributions")]
        public AspectDistribution[] AspectDistributions { get; set; }

        /// <summary>
        /// An array of containers for the all the buying option refinements.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "buyingOptionDistributions")]
        public BuyingOptionDistribution[] BuyingOptionDistributions { get; set; }

        /// <summary>
        /// An array of containers for the all the category refinements.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "categoryDistributions")]
        public CategoryDistribution[] CategoryDistributions { get; set; }

        /// <summary>
        /// An array of containers for the all the condition refinements.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "conditionDistributions")]
        public ConditionDistribution[] ConditionDistributions { get; set; }

        /// <summary>
        /// The identifier of the category that most of the items are part of. 
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "dominantCategoryId")]
        public string DominantCategoryId { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for the image information.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class SearchByImageRequest
    {

        /// <summary>
        /// The Base64 string of the image.<br><br>To get the Base64 image string, you can use sites such as <a href="https://codebeautify.org/image-to-base64-converter " target="_blank">https://codebeautify.org/image-to-base64-converter</a>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "image")]
        public string Image { get; set; }
    }

    /// <summary>
    /// The type that defines the fields for a paginated result set. The response consists of 0 or more sequenced <i>pages</i> where each page has 0 or more items.
    /// </summary>
    [System.Runtime.Serialization.DataContract(Namespace = "http://browseapi.ebay/2020/03")]
    public class SearchPagedCollection
    {

        [System.Runtime.Serialization.DataMember(Name = "autoCorrections")]
        public AutoCorrections AutoCorrections { get; set; }

        /// <summary>
        /// The URI of the current page of results.<br><br>The following example of the <b>search</b> method returns items 1 thru 5 from the list of items found.<pre>https://api.ebay.com/buy/v1/item_summary/search?q=shirt&limit=5&offset=0</pre>.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "href")]
        public string Href { get; set; }

        /// <summary>
        /// An array of the items on this page. The items are sorted according to the sorting method specified in the request.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "itemSummaries")]
        public ItemSummary[] ItemSummaries { get; set; }

        /// <summary>
        /// The value of the <code>limit</code> parameter submitted in the request, which is the maximum number of items to return on a page, from the result set. A result set is the complete set of items returned by the method.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "limit")]
        public System.Nullable<System.Int32> Limit { get; set; }

        /// <summary>
        /// The URI for the next page of results. This value is returned if there is an additional page of results to return from the result set.<br><br>The following example of the <b>search</b> method returns items 5 thru 10 from the list of items found.<pre>https://api.ebay.com/buy/v1/item_summary/search?query=t-shirts&limit=5&offset=10</pre>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "next")]
        public string Next { get; set; }

        /// <summary>
        /// This value indicates the <code>offset</code> used for current page of items being returned. Assume the initial request used an <code>offset</code> of <code>0</code> and a <code>limit</code> of <code>3</code>. Then in the first page of results, this value would be <code>0</code>, and items 1-3 are returned. For the second page, this value is <code>3</code> and so on.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "offset")]
        public System.Nullable<System.Int32> Offset { get; set; }

        /// <summary>
        /// The URI for the previous page of results. This is returned if there is a previous page of results from the result set.<br><br>The following example of the <b>search</b> method returns items 1 thru 5 from the list of items found, which would be the first set of items returned.<pre>https://api.ebay.com/buy/v1/item_summary/search?query=t-shirts&limit=5&offset=0</pre>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "prev")]
        public string Prev { get; set; }

        /// <summary>
        /// This type defines the fields for the various refinements of an item. You can use the information in this container to create histograms, which help shoppers choose exactly what they want.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "refinement")]
        public Refinement Refinement { get; set; }

        /// <summary>
        /// The total number of items that match the input criteria.<br><br><span class="tablenote"><b>Note:</b> <code>total</code> is just an indicator of the number of listings for a given query. It could vary based on the number of listings with variations included in the result. It is strongly recommended that <code>total</code> not be used in pagination use cases. Instead, use <a href="/api-docs/buy/browse/resources/item_summary/methods/search#response.next ">next</a> to determine the results on the next page.</span>
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "total")]
        public System.Nullable<System.Int32> Total { get; set; }

        /// <summary>
        /// The container with all the warnings for the request.
        /// </summary>
        [System.Runtime.Serialization.DataMember(Name = "warnings")]
        public Error[] Warnings { get; set; }
    }

    public partial class BrowseAPIClass
    {

        private System.Net.Http.HttpClient httpClient;

        private JsonSerializerSettings jsonSerializerSettings;

        public BrowseAPIClass(System.Net.Http.HttpClient httpClient, JsonSerializerSettings jsonSerializerSettings = null)
        {
            if (httpClient == null)
                throw new ArgumentNullException("Null HttpClient.", "httpClient");

            if (httpClient.BaseAddress == null)
                throw new ArgumentNullException("HttpClient has no BaseAddress", "httpClient");

            this.httpClient = httpClient;
            this.jsonSerializerSettings = jsonSerializerSettings;
        }

        /// <summary>
        /// This method searches for eBay items by various query parameters and retrieves summaries of the items. You can search by keyword, category, eBay product ID (ePID), or GTIN, charity ID, or a combination of these.<br><br><span class="tablenote"><b>Note:</b> Only listings where <code>FIXED_PRICE</code> (Buy It Now) is a buying option are returned by default. To retrieve listings that do not have <code>FIXED_PRICE</code> as a buying option, the <code>buyingOptions</code> filter can be used to retrieve those listings.<br><br>Note that an auction listing enabled with the <i>Buy it Now</i> feature will initially show <code>AUCTION</code> and <code>FIXED_PRICE</code> as buying options, but if/when that auction listing receives a qualifying bid, only <code>AUCTION</code> remains as a buying option. If this happens, the <code>buyingOptions</code> filter would need to be used to retrieve that auction listing.</span><br>This method also supports the following:<ul><li>Filtering by the value of one or multiple fields, such as listing format, item condition, price range, location, and more. For the fields supported by this method, refer to the <a href="#uri.filter">filter</a> parameter.</li><li>Retrieving the refinements (metadata) of an item, such as item aspects (color, brand) condition, category, etc. using the <a href="#uri.fieldgroups">fieldgroups</a> parameter.</li><li>Filtering by item aspects and other refinements using the <a href="#uri.aspect_filter">aspect_filter</a> parameter.</li><li>Filtering for items that are compatible with a specific product, using the <a href="#uri.compatibility_filter">compatibility_filter</a> parameter.</li><li>Creating aspects histograms, which enables shoppers to drill down in each refinement narrowing the search results.</li></ul>For additional information and examples of these capabilities, refer to <a href="/api-docs/buy/static/api-browse.html" target="_blank">Browse API</a> in the Buying Integration Guide.</br><h3>Pagination and sort controls</h3>There are pagination controls (<b>limit</b> and <b>offset</b> fields) and <b>sort</b> query parameters that control/sort the data that are returned. By default, results are sorted by <i>Best Match</i>. For more information about Best Match, refer to <a href="https://pages.ebay.com/help/sell/searchstanding.html " target="_blank">Best Match</a>.</br><h3>Restrictions</h3>This method can return a maximum of 10,000 items. For a list of supported sites and other restrictions, refer to <a href="/api-docs/buy/browse/overview.html#API">API Restrictions</a>.<br><br><span class="tablenote"><b>eBay Partner Network:</b> In order to receive a commission for your sales, you must use the URL returned in the <code>itemAffiliateWebUrl</code> field to forward your buyer to the ebay.com site.</span>
        /// Search item_summary/search
        /// </summary>
        /// <param name="aspect_filter">This field lets you filter by item aspects. The aspect name/value pairs and category, which is required, is used to limit the results to specific aspects of the item. For example, in a clothing category one aspect pair would be Color/Red.<br><br><span class="tablenote"><b>Note:</b> The category ID must be specified <i>twice</i>:<ul><li>Once as a URI parameter in the <code>category_ids</code> field</li><li>Once as part of the <code>aspect_filter</code> field</li></ul>These two values <b>must</b> be the same.</span><br>For example, to return items for a woman's red shirt, issue the following request:<br><pre>/buy/browse/v1/item_summary/search?q=shirt&category_ids=15724&aspect_filter=categoryId:15724,Color:{Red}</pre>To get a list of the aspect pairs and the category, which is returned in the <code>dominantCategoryId</code> field, set <code>fieldgroups</code> to <code>ASPECT_REFINEMENTS</code> as illustrated here:<pre>/buy/browse/v1/item_summary/search?q=shirt&amp;fieldgroups=ASPECT_REFINEMENTS</pre><span class="tablenote"><b> Note:</b> The pipe symbol is used as a delimiter between aspect filter values. If a value contains a pipe symbol (for example, the brand name 'Bed|Stü'), you must enter a backslash before the pipe character to prevent it from being evaluated as a delimiter.<br><br>The following example illustrates the correct format for entering two brand names as aspect filter values, one of which contains a pipe symbol:<pre>/buy/browse/v1/item_summary/search?limit=50&category_ids=3034&filter=buyingOptions:{AUCTION|FIXED_PRICE}&aspect_filter=categoryId:3034,Brand:{Bed&bsol;|Stü|Nike}</pre></span> For implementation help, refer to eBay API documentation at https://developer.ebay.com/api-docs/buy/browse/types/gct:AspectFilter</param>
        /// <param name="auto_correct">A query parameter that enables auto correction.<br><br>A sample is shown below:<pre>/buy/browse/v1/item_summary/search?auto_correct=KEYWORD</pre><span class="tablenote"><b>Note:</b> Auto correction is currently supported in the following marketplaces:<ul><li>United States</li><li>Austria</li><li>Australia</li><li>Canada</li><li>Switzerland</li><li>Germany</li><li>Spain</li><li>France</li><li>United Kingdom</li><li>Ireland</li><li>Italy</li></ul></span><br><b>Valid Values:</b> <code>KEYWORD</code></param>
        /// <param name="category_ids"><a name="category_ids"></a>The category ID is used to limit the results that are returned. This field may pass in one category ID or a comma separated list of IDs as illustrated in the following examples:<pre>/buy/browse/v1/item_summary/search?category_ids=29792</pre><pre>/buy/browse/v1/item_summary/search?category_ids=267,29792</pre><span class="tablenote"><b>Note:</b> Currently, you can pass in only one category ID per request.</span><br>To refine the set of information that is returned, <code>category_ids</code> may be combined with <b>EITHER</b>:<ul><li><code>epid</code> and/or <code>gtin</code> values</li><li><code>q</code> keywords</li></ul>For example, when looking of a toy phone, simply searching for "phone" will return mobile phones because that is the "Best Match" for the search. To further refine the request to include toy phones, include the <b>Toys &amp; Hobbies</b> category ID as illustrated here:<pre>/buy/browse/v1/item_summary/search?q=phone&category_ids=220</pre>Because the list of eBay category IDs is not published and category IDs are not the same across all eBay marketplaces, category IDs may be determined by:<ul><li>Visiting the <a href="https://pages.ebay.com/sellerinformation/news/categorychanges.html " target="_blank">Category Changes page</a></li><li>Using the Taxonomy API. Refer to <a href="/api-docs/buy/buy-categories.html" target="_blank">Get Categories for Buy APIs</a> for complete information.</li><li>Issuing the following call to retrieve the <code>dominantCategoryId</code> for an item:<pre>/buy/browse/v1/item_summary/search?q=<i> keyword</i>&fieldgroups=ASPECT_REFINEMENTS</pre></li></ul><span class="tablenote"><b>Note:</b> If a top-level (L1) category is specified, you <b>must</b> also include a <code>q</code> query parameter.</span></param>
        /// <param name="charity_ids">The charity ID filters results to return only those items associated with the specified charity.<br><br><span class="tablenote"><b>Note:</b> <code>charity_ids</code> is only supported by the US and UK marketplaces.</span><br>Charity ID is a charity's unique identification number:<ul><li>In the US, this is the <b>Employer Identification Number (EIN)</b>.</li><li>In the UK, this is the Charity Registration Number (CRN), commonly called <b>Charity Number</b>.</li></ul><br><code>charity_ids</code> may be retrieved/determined as follows:<ul><li>Search for a supported charity using the Charity API's <a href="/api-docs/commerce/charity/resources/charity_org/methods/getCharityOrgs " target="_blank">getCharityOrgs</a> method.<br><br><div class="msgbox_important"><p class="msgbox_importantInDiv" data-mc-autonum="&lt;b&gt;&lt;span style=&quot;color: #dd1e31;&quot; class=&quot;mcFormatColor&quot;&gt;Important! &lt;/span&gt;&lt;/b&gt;"><span class="autonumber"><span><b><span style="color: #dd1e31;" class="mcFormatColor">Important!</span></b></span></span> The value to be passed in as <code>charity_ids</code> is that returned in the <code>registrationId</code> field.</p></div></li><li>Search for a supported charity by visiting <a href="https://charity.ebay.com/search " target="_blank">Charity Search</a>. Click on the charity's name to display its information. The EIN number is included in the charity's information block at the top of the page.<br><br>For example, the charity ID for <a href="https://charity.ebay.com/charity/American-Red-Cross/3843 " target="_blank">American Red Cross</a>, is <code>530196605</code>.</li></ul>Up to 20 comma-separated <code>charity_ids</code> may be specified in each query. Additionally, <code>charity_ids</code> may be combined with <code>category_ids</code> and/or <code>q</code> keyword values to further filter returned results.<br><br>A sample query using <code>charity_ids</code> is:<br><pre>/buy/browse/v1/item_summary/search?charity_ids=13-1788491,300108469</pre></param>
        /// <param name="compatibility_filter">This field specifies the attributes used to define a specific product. The service searches for items matching the keyword, or matching the keyword and a product attribute value in the title of the item.<br><br><span class="tablenote"><b>Note:</b> The only products supported are cars, trucks, and motorcycles.</span><br>For example, if the keyword is <code>brakes</code> and <code>compatibility-filter=Year:2018;Make:BMW</code>, the items returned are items with <b>brakes</b>, <b>2018</b>, or <b>BMW</b> in the title.<br><br>The service uses the product attributes to determine whether the item is compatible. The service returns the attributes that are compatible and the <a href="/api-docs/buy/browse/resources/item_summary/methods/search#response.itemSummaries.compatibilityMatch">CompatibilityMatchEnum</a> value that indicates how well the item matches the attributes.<br><br><div class="msgbox_tip"><p class="msgbox_tipInDiv" data-mc-autonum="&lt;b&gt;&lt;span style=&quot;color: #478415;&quot; class=&quot;mcFormatColor&quot;&gt;Tip: &lt;/span&gt;&lt;/b&gt;"><span class="autonumber"><span><b><span style="color: #478415;" class="mcFormatColor">Tip:</span></b></span></span> Refer to the <a href="/api-docs/buy/browse/resources/item_summary/methods/search#s0-1-21-6-7-7-6-ReturnItemsthatareCompatiblewiththeKeywordandVehicle-9" target="_blank">Samples</a> section for a detailed example.</p></div><br><b>Best Practice:</b> Submit all of the <a href="/api-docs/buy/static/api-browse.html#product-attributes" target="_blank">product attributes</a> for the specific product.<br><br>To find the attributes and values for a specific marketplace, use the <a href="/api-docs/commerce/taxonomy/resources/category_tree/methods/getCompatibilityProperties" target="_blank">getCompatibilityProperties</a> method in the <a href="/api-docs/commerce/taxonomy/resources/methods" target="_blank">Taxonomy API</a>.<br><br>For more information, refer to <a href="/api-docs/buy/static/api-browse.html#Check" target="_blank">Check compatibility</a> in the Buying Integration Guide.<br><br><span class="tablenote"><b>Note:</b> Testing in Sandbox is only supported using mock data. Refer to <a href="/api-docs/buy/static/api-browse.html#sbox-test" target="_blank">Testing search in the Sandbox</a> for details.</span><br><b>Required:</b><ul><li><code>q</code> (keyword)</li><li>One parts-compatibility-supported category ID (such as <code>33559</code> Brakes)</li><li>At least one <a href="/api-docs/buy/static/api-browse.html#product-attributes" target="_blank">vehicle attribute</a> name/value pair</li></ul> For implementation help, refer to eBay API documentation at https://developer.ebay.com/api-docs/buy/browse/types/gct:CompatibilityFilter</param>
        /// <param name="epid">The ePID is the eBay product identifier of a product from the eBay product catalog. This field limits the results to only items in the specified ePID.<br><br>Use the <a href="/api-docs/commerce/catalog/resources/product_summary/methods/search" target="_blank">product_summary/search</a> method in the <a href="/api-docs/commerce/catalog/overview.html" target="_blank">Catalog</a> API to search for the ePID of the product.<br><br>For example:<pre>/buy/browse/v1/item_summary/search?epid=15032</pre><span class="tablenote"><b>Note:</b> When constructing a query, <code>epid</code> may be combined with a <code>gtin</code> value. However, <i>do not</i> specify keywords using the <code>q</code> parameter — keywords cannot be used in conjunction with an <code>epid</code>.</span></param>
        /// <param name="fieldgroups">A comma-separated list of values that controls what is returned in the response. The default is <code>MATCHING_ITEMS</code>, which returns the items that match the keyword or category specified. The other values return data that can be used to create histograms or provide additional information.<br><br><b>Valid Values:</b><ul><li><code>ASPECT_REFINEMENTS</code><br>This field group adds the <a href="#response.refinement.aspectDistributions">aspectDistributions</a> container to the response.<br><br><span class="tablenote"><b>Note:</b> Information returned by <code>ASPECT_REFINEMENTS</code> is category specific.</span></li><li><code>BUYING_OPTION_REFINEMENTS</code><br>This field group adds the <a href="#response.refinement.buyingOptionDistributions">buyingOptionDistributions</a> container to the response.</li><li><code>CATEGORY_REFINEMENTS</code><br>This field group adds the <a href="#response.refinement.categoryDistributions">categoryDistributions</a> container to the response.</li><li><code>CONDITION_REFINEMENTS</code><br>This field group adds the <a href="#response.refinement.conditionDistributions">conditionDistributions</a> containers, such as <code>NEW</code>, <code>USED</code>, etc., to the response. Within these groups are multiple states of the condition.<br><br>For example, <code>NEW</code> can be <i>New without tag</i>, <i>New in box</i>, <i>New without box</i>, etc.</li><li><code>EXTENDED</code><br>This field group adds the following fields to the response:<ul><li><a href="/api-docs/buy/browse/resources/item_summary/methods/search#response.itemSummaries.shortDescription">shortDescription</a></li><li><a href="/api-docs/buy/browse/resources/item_summary/methods/search#response.itemSummaries.itemLocation.city">itemLocation.city</a></li></ul></li><li><code>MATCHING_ITEMS</code> (<b>default value</b>)<br>This field group is intended to be used with one or more of the refinement values listed above. This is used to return the specified refinements and all matching items.</li><li><code>FULL</code><br>This field group returns all refinement containers and all matching items.</li></ul><b>Default:</b> <code>MATCHING_ITEMS</code></param>
        /// <param name="filter">An array of field filters that can be used to limit/customize the result set.<br><br>Refer to <a href="/api-docs/buy/static/ref-buy-browse-filters.html" target="_blank">Buy API Field Filters</a> for additional information and examples of all supported filters.<br><br>For example, to filter shirts based on a specific range of prices, include the filter illustrated here: <pre>/buy/browse/v1/item_summary/search?q=shirt&filter=price:[10..50]</pre>Filters may also be combined within a single request as illustrated in the sample below which further refines results to return only those shirts available from specific sellers: <pre>/buy/browse/v1/item_summary/search?q=shirt&filter=price:[10..50],sellers:{rpseller|bigSal}</pre> For implementation help, refer to eBay API documentation at https://developer.ebay.com/api-docs/buy/browse/types/cos:FilterField</param>
        /// <param name="gtin">This field lets you search by the Global Trade Item Number of an item as defined by <a href="https://www.gtin.info " target="_blank">https://www.gtin.info</a>.<br><br>For example:<br><pre>/buy/browse/v1/item_summary/search?gtin=099482432621</pre><span class="tablenote"><b>Note:</b> You can search only by UPC (Universal Product Code). To search using another GTIN format, you must search by keyword.</span><span class="tablenote"><b>Note:</b> When constructing a query, <code>gtin</code> may be combined with an <code>epid</code> value. However, <i>do not</i> specify keywords using the <code>q</code> parameter — keywords cannot be used in conjunction with <code>gtin</code>.</span></param>
        /// <param name="limit">The number of items from the result set returned in a single page.<br><br><span class="tablenote"><b>Note:</b> If a value is set in the <code>limit</code> field, the value of <code>offset</code> must be either zero or a multiple of the <code>limit</code> value. An error is returned for invalid <code>offset</code> values.</span><br><span class="tablenote"><b>Note:</b> This method can return a maximum of 10,000 items in one results set.</span><br><b>Min:</b> 1<br><br><b>Max:</b> 200<br><br><b>Default:</b> 50</param>
        /// <param name="offset">Specifies the number of items to skip in the result set. This is used with the <code>limit</code> field to control the pagination of the output.<br><br>For example:<ul><li>If <code>offset</code> is 0 and <code>limit</code> is 10, the method will retrieve items 1-10 from the list of items returned</li><li>If <code>offset</code> is 10 and <code>limit</code> is 10, the method will retrieve items 11-20 from the list of items returned.</li></ul><span class="tablenote"><b>Note:</b> The value of <code>offset</code> must be either zero or a multiple of the value set in the <code>limit</code> field. An error is returned for invalid <code>offset</code> values.</span><br><span class="tablenote"><b>Note:</b> This method can return a maximum of 10,000 items in one results set.</span><br><b>Min</b>: 0<br><br><b>Max:</b> 9,999<br><br><b>Default:</b> 0</param>
        /// <param name="q">A string consisting of one or more keywords used to search for items on eBay.<br><br><span class="tablenote"><b>Note:</b> The <code>*</code> wildcard character is <b>not</b> allowed in this field.</span><br>When providing two or more keywords in a single query, the string is processed as follows:<ul><li>When successive keywords are separated by a space, the list of keywords is processed as an <code>AND</code> request. For example, to retrieve items that include <b>both</b> of the keywords <b>iphone</b> AND <b>ipad</b>, submit the following query:<br><pre>/buy/browse/v1/item_summary/search?q=iphone ipad</pre></li><li>When successive keywords are comma-separated and surrounded by a single pair of parentheses, OR if the keywords are each URL-encoded, the list of keywords is processed as an <code>OR</code> request. For example, to retrieve items that include <b>iphone</b> OR <b>ipad</b>, submit one of the following queries:<br><pre>/buy/browse/v1/item_summary/search?q=(iphone, ipad)</pre><pre>/buy/browse/v1/item_summary/search?q=%28iphone%2c%20ipad%29</pre></li></ul><br><span class="tablenote"><b>Note:</b> When specifying keywords using the <code>q</code> parameter, <i>do not include</i> an <code>epid</code> or <code>gtin</code> parameter value as neither can be used in conjuction with a keyword search.</span></param>
        /// <param name="sort">Specifies the criteria on which returned items are to be sorted.<br><br>Items can be sorted in ascending order based on:<ul><li>Price (<code>sort=price</code>)<br>Returned items are sorted based on their total cost (i.e., <i>price + shipping cost</i>).<br><br>Items with the lowest combined price are shown first.<br><br>When sorting by price, it is highly recommended that:<ul><li>The <code>X-EBAY-C-ENDUSERCTX</code> request header is used, <b>and</b></li><li>The <code>contextualLocation</code> parameter specifies the delivery country and postal code.<br><br>These values must be <i>URL-encoded</i>.</li></ul>Refer to the following example:<pre>X-EBAY-C-ENDUSERCTX: contextualLocation=country%3DUS%2Czip%3D19406</pre><b>Descending price order</b><br>To sort items from <i>highest price</i> to <i>lowest price</i> insert a minus sign (-) before <code>price</code> as the search criterion:<pre>sort=-price</pre></li><li>Distance (<code>sort=distance</code>)<br>Returned items are sorted based on their distance from the buyer's location.<br><br>Items that are closest to the buyer are listed first.<br><br><div class="msgbox_important"><p class="msgbox_importantInDiv" data-mc-autonum="&lt;b&gt;&lt;span style=&quot;color: #dd1e31;&quot; class=&quot;mcFormatColor&quot;&gt;Important! &lt;/span&gt;&lt;/b&gt;"><span class="autonumber"><span><b><span style="color: #dd1e31;" class="mcFormatColor">Important!</span></b></span></span> When sorting by distance, all required <a href="/api-docs/buy/static/ref-buy-browse-filters.html#pickupCountry" target="_blank">pickup filters</a> must be included in the request.</p></div></li><li>Date<br>Items can be sorted by date based on:<ul><li>Listing date (<code>sort=newlyListed</code>)<br>Returned items are sorted based on their listing date.<br><br>Newly listed items are shown first.</li><li>End date (<code>sort=endingSoonest</code>)<br>Returned items are sorted based on the date/time on which their listing is scheduled to end.<br><br>Items that are closest to their scheduled ending date/time are shown first.</li></ul>If no sort parameter is submitted, the result set is sorted by "Best Match". Refer to <a href="https://www.ebay.com/help/selling/listings/listing-tips/optimising-listings-best-match?id=4166 " target="_blank">Optimizing your listings for Best Match</a> for additional information. For implementation help, refer to eBay API documentation at https://developer.ebay.com/api-docs/buy/browse/types/cos:SortField</param>
        /// <returns>OK</returns>
        public async Task<SearchPagedCollection> SearchAsync(string aspect_filter, string auto_correct, string category_ids, string charity_ids, string compatibility_filter, string epid, string fieldgroups, string filter, string gtin, string limit, string offset, string q, string sort, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
        {
            //var requestUri = "item_summary/search?aspect_filter=" + (aspect_filter == null ? "" : System.Uri.EscapeDataString(aspect_filter)) + "&auto_correct=" + (auto_correct == null ? "" : System.Uri.EscapeDataString(auto_correct)) + "&category_ids=" + (category_ids == null ? "" : System.Uri.EscapeDataString(category_ids)) + "&charity_ids=" + (charity_ids == null ? "" : System.Uri.EscapeDataString(charity_ids)) + "&compatibility_filter=" + (compatibility_filter == null ? "" : System.Uri.EscapeDataString(compatibility_filter)) + "&epid=" + (epid == null ? "" : System.Uri.EscapeDataString(epid)) + "&fieldgroups=" + (fieldgroups == null ? "" : System.Uri.EscapeDataString(fieldgroups)) + "&filter=" + (filter == null ? "" : System.Uri.EscapeDataString(filter)) + "&gtin=" + (gtin == null ? "" : System.Uri.EscapeDataString(gtin)) + "&limit=" + (limit == null ? "" : System.Uri.EscapeDataString(limit)) + "&offset=" + (offset == null ? "" : System.Uri.EscapeDataString(offset)) + "&q=" + (q == null ? "" : System.Uri.EscapeDataString(q)) + "&sort=" + (sort == null ? "" : System.Uri.EscapeDataString(sort));
            var requestUri = "item_summary/search"
                                         + (limit == null ? "?limit=100" : "?limit=" + Uri.EscapeDataString(limit))
                                         + (aspect_filter == null ? "" : "&aspect_filter=" + Uri.EscapeDataString(aspect_filter))
                                         + (auto_correct == null ? "" : "&auto_correct=" + Uri.EscapeDataString(auto_correct))
                                         + (category_ids == null ? "" : "&category_ids=" + Uri.EscapeDataString(category_ids))
                                         + (charity_ids == null ? "" : "&charity_ids=" + Uri.EscapeDataString(charity_ids))
                                         + (compatibility_filter == null
                                             ? ""
                                             : "&compatibility_filter=" + Uri.EscapeDataString(compatibility_filter))
                                         + (epid == null ? "" : "&epid=" + Uri.EscapeDataString(epid))
                                         + (fieldgroups == null ? "" : "&fieldgroups=" + Uri.EscapeDataString(fieldgroups))
                                         + (filter == null ? "" : "&filter=" + Uri.EscapeDataString(filter))
                                         + (gtin == null ? "" : "&gtin=" + Uri.EscapeDataString(gtin))
                                         + (offset == null ? "" : "&offset=" + Uri.EscapeDataString(offset))
                                         + (q == null ? "" : "&q=" + Uri.EscapeDataString(q))
                                         + (sort == null ? "" : "&sort=" + Uri.EscapeDataString(sort));
            using var httpRequestMessage = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Get, requestUri);
            if (handleHeaders != null)
            {
                handleHeaders(httpRequestMessage.Headers);
            }

            var responseMessage = await httpClient.SendAsync(httpRequestMessage);
            try
            {
                responseMessage.EnsureSuccessStatusCodeEx();
                var streamContent = await responseMessage.Content.ReadAsStreamAsync();
                using JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(streamContent));
                var serializer = JsonSerializer.Create(jsonSerializerSettings);
                return serializer.Deserialize<SearchPagedCollection>(jsonReader);
            }
            finally
            {
                responseMessage.Dispose();
            }
        }

        public async Task<SearchPagedCollection> SearchByUrlAsync(string requestUri, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
        {
            using (var httpRequestMessage = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Get, requestUri) { Version = new Version(2, 0) })
            {
                if (handleHeaders != null)
                {
                    handleHeaders(httpRequestMessage.Headers);
                }

                var responseMessage = await httpClient.SendAsync(httpRequestMessage);
                try
                {
                    responseMessage.EnsureSuccessStatusCodeEx();
                    var streamContent = await responseMessage.Content.ReadAsStreamAsync();
                    using JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(streamContent));
                    var serializer = JsonSerializer.Create(jsonSerializerSettings);
                    return serializer.Deserialize<SearchPagedCollection>(jsonReader);
                }
                finally
                {
                    responseMessage.Dispose();
                }
            }
        }

        /// <summary>
        /// This method searches for eBay items based on a image and retrieves summaries of the items. You pass in a Base64 image in the request payload and can refine the search by category, or with other available filters.<br><br>To get the Base64 image string, you can use sites such as <a href="https://codebeautify.org/image-to-base64-converter " target="_blank">https://codebeautify.org/image-to-base64-converter</a>.<br><br>This method also supports the following:<ul> <li>Filtering by the value of one or multiple fields, such as listing format, item condition, price range, location, and more. For the fields supported by this method, refer to the <a href="#uri.filter">filter</a> parameter.</li><li>Filtering by item aspects using the <a href="#uri.aspect_filter">aspect_filter</a> parameter.</li></ul>For details and examples of these capabilities, refer to <a href="/api-docs/buy/static/api-browse.html" target="_blank">Browse API</a> in the Buying Integration Guide.<h3>URL Encoding for Parameters</h3>Query parameter values need to be URL encoded. For details, refer to <a href="/api-docs/static/rest-request-components.html#parameters" target="_blank">URL encoding query parameter values</a>. For readability, code examples in this document have not been URL encoded.<h3>Restrictions</h3>This method can return a maximum of 10,000 items. For a list of supported sites and other restrictions, refer to <a href="/api-docs/buy/browse/overview.html#API">API Restrictions</a>.<br><br><span class="tablenote"><b>eBay Partner Network:</b> In order to receive a commission for your sales, you must use the URL returned in the <code>itemAffiliateWebUrl</code> field to forward your buyer to the ebay.com site.</span>
        /// SearchByImage item_summary/search_by_image
        /// </summary>
        /// <param name="aspect_filter">This field lets you filter by item aspects. The aspect name/value pairs and category, which is required, is used to limit the results to specific aspects of the item. For example, in a clothing category one aspect pair would be Color/Red.<br><br><span class="tablenote"><b>Note:</b> The category ID must be specified <i>twice</i>:<ul><li>Once as a URI parameter in the <code>category_ids</code> field</li><li>Once as part of the <code>aspect_filter</code> field</li></ul>These two values <b>must</b> be the same.</span><br>For example, to return items for a woman's red shirt, issue the following request:<br><pre>/buy/browse/v1/item_summary/search?q=shirt&category_ids=15724&aspect_filter=categoryId:15724,Color:{Red}</pre>To get a list of the aspect pairs and the category, which is returned in the <code>dominantCategoryId</code> field, set <code>fieldgroups</code> to <code>ASPECT_REFINEMENTS</code> as illustrated here:<pre>/buy/browse/v1/item_summary/search?q=shirt&amp;fieldgroups=ASPECT_REFINEMENTS</pre><span class="tablenote"><b> Note:</b> The pipe symbol is used as a delimiter between aspect filter values. If a value contains a pipe symbol (for example, the brand name 'Bed|Stü'), you must enter a backslash before the pipe character to prevent it from being evaluated as a delimiter.<br><br>The following example illustrates the correct format for entering two brand names as aspect filter values, one of which contains a pipe symbol:<pre>/buy/browse/v1/item_summary/search?limit=50&category_ids=3034&filter=buyingOptions:{AUCTION|FIXED_PRICE}&aspect_filter=categoryId:3034,Brand:{Bed&bsol;|Stü|Nike}</pre></span> For implementation help, refer to eBay API documentation at https://developer.ebay.com/api-docs/buy/browse/types/gct:AspectFilter</param>
        /// <param name="category_ids"></a>The category ID is used to limit the results that are returned. This field may pass in one category ID or a comma separated list of IDs as illustrated in the following examples:<pre>/buy/browse/v1/item_summary/searchByImage?category_ids=29792</pre><pre>/buy/browse/v1/item_summary/searchByImage?category_ids=267,29792</pre><span class="tablenote"><b>Note:</b> Currently, you can pass in only one category ID per request.</span><br>To refine the set of information that is returned, <code>category_ids</code> may be combined with other available filters.<br><br>Because the list of eBay category IDs is not published and category IDs are not the same across all eBay marketplaces, category IDs may be determined by:<ul><li>Visiting the <a href="https://pages.ebay.com/sellerinformation/news/categorychanges.html " target="_blank">Category Changes page</a></li><li>Using the Taxonomy API. Refer to <a href="/api-docs/buy/buy-categories.html" target="_blank">Get Categories for Buy APIs</a> for complete information.</li><li>Issuing the following call to retrieve the <code>dominantCategoryId</code> for an item:<pre>/buy/browse/v1/item_summary/searchByImage?q=<i> keyword</i>&fieldgroups=ASPECT_REFINEMENTS</pre></li></ul></param>
        /// <param name="charity_ids">The charity ID filters results to return only those items associated with the specified charity.<br><br><span class="tablenote"><b>Note:</b> <code>charity_ids</code> is only supported by the US and UK marketplaces.</span><br>Charity ID is a charity's unique identification number:<ul><li>In the US, this is the <b>Employer Identification Number (EIN)</b>.</li><li>In the UK, this is the Charity Registration Number (CRN), commonly called <b>Charity Number</b>.</li></ul><br><code>charity_ids</code> may be retrieved/determined as follows:<ul><li>Search for a supported charity using the Charity API's <a href="/api-docs/commerce/charity/resources/charity_org/methods/getCharityOrgs " target="_blank">getCharityOrgs</a> method.<br><br><div class="msgbox_important"><p class="msgbox_importantInDiv" data-mc-autonum="&lt;b&gt;&lt;span style=&quot;color: #dd1e31;&quot; class=&quot;mcFormatColor&quot;&gt;Important! &lt;/span&gt;&lt;/b&gt;"><span class="autonumber"><span><b><span style="color: #dd1e31;" class="mcFormatColor">Important!</span></b></span></span> The value to be passed in as <code>charity_ids</code> is that returned in the <code>registrationId</code> field.</p></div></li><li>Search for a supported charity by visiting <a href="https://charity.ebay.com/search " target="_blank">Charity Search</a>. Click on the charity's name to display its information. The EIN number is included in the charity's information block at the top of the page.<br><br>For example, the charity ID for <a href="https://charity.ebay.com/charity/American-Red-Cross/3843 " target="_blank">American Red Cross</a>, is <code>530196605</code>.</li></ul>Up to 20 comma-separated <code>charity_ids</code> may be specified in each query. Additionally, <code>charity_ids</code> may be combined with <code>category_ids</code> and/or <code>q</code> keyword values to further filter returned results.<br><br>A sample query using <code>charity_ids</code> is:<br><pre>/buy/browse/v1/item_summary/search?charity_ids=13-1788491,300108469</pre></param>
        /// <param name="fieldgroups">A comma-separated list of values that controls what is returned in the response. The default is <code>MATCHING_ITEMS</code>, which returns the items that match the keyword or category specified. The other values return data that can be used to create histograms or provide additional information.<br><br><b>Valid Values:</b><ul><li><code>ASPECT_REFINEMENTS</code><br>This field group adds the <a href="#response.refinement.aspectDistributions">aspectDistributions</a> container to the response.<br><br><span class="tablenote"><b>Note:</b> Information returned by <code>ASPECT_REFINEMENTS</code> is category specific.</span></li><li><code>BUYING_OPTION_REFINEMENTS</code><br>This field group adds the <a href="#response.refinement.buyingOptionDistributions">buyingOptionDistributions</a> container to the response.</li><li><code>CATEGORY_REFINEMENTS</code><br>This field group adds the <a href="#response.refinement.categoryDistributions">categoryDistributions</a> container to the response.</li><li><code>CONDITION_REFINEMENTS</code><br>This field group adds the <a href="#response.refinement.conditionDistributions">conditionDistributions</a> containers, such as <code>NEW</code>, <code>USED</code>, etc., to the response. Within these groups are multiple states of the condition.<br><br>For example, <code>NEW</code> can be <i>New without tag</i>, <i>New in box</i>, <i>New without box</i>, etc.</li><li><code>EXTENDED</code><br>This field group adds the following fields to the response:<ul><li><a href="/api-docs/buy/browse/resources/item_summary/methods/search#response.itemSummaries.shortDescription">shortDescription</a></li><li><a href="/api-docs/buy/browse/resources/item_summary/methods/search#response.itemSummaries.itemLocation.city">itemLocation.city</a></li></ul></li><li><code>MATCHING_ITEMS</code> (<b>default value</b>)<br>This field group is intended to be used with one or more of the refinement values listed above. This is used to return the specified refinements and all matching items.</li><li><code>FULL</code><br>This field group returns all refinement containers and all matching items.</li></ul><b>Default:</b> <code>MATCHING_ITEMS</code></param>
        /// <param name="filter">An array of field filters that can be used to limit/customize the result set.<br><br>Refer to <a href="/api-docs/buy/static/ref-buy-browse-filters.html" target="_blank">Buy API Field Filters</a> for the information about available filters.<br><br>For example, to filter shirts based on a specific range of prices, include the filter illustrated here: <pre>/buy/browse/v1/item_summary/search?q=shirt&filter=price:[10..50]</pre>Filters may also be combined within a single request as illustrated in the sample below which further refines results to return only those shirts available from specific sellers: <pre>/buy/browse/v1/item_summary/search?q=shirt&filter=price:[10..50],sellers:{rpseller|bigSal}</pre><span class="tablenote"><b>Note:</b> Refer to <a href="/api-docs/buy/static/ref-buy-browse-filters.html" target="_blank">Buy API Field Filters</a> for additional information and examples of all supported filters.</span> For implementation help, refer to eBay API documentation at https://developer.ebay.com/api-docs/buy/browse/types/cos:FilterField</param>
        /// <param name="limit">The number of items from the result set returned in a single page.<br><br><span class="tablenote"><b>Note:</b> If a value is set in the <code>limit</code> field, the value of <code>offset</code> must be either zero or a multiple of the <code>limit</code> value. An error is returned for invalid <code>offset</code> values.</span><br><span class="tablenote"><b>Note:</b> This method can return a maximum of 10,000 items in one results set.</span><br><b>Min:</b> 1<br><br><b>Max:</b> 200<br><br><b>Default:</b> 50</param>
        /// <param name="offset">Specifies the number of items to skip in the result set. This is used with the <code>limit</code> field to control the pagination of the output.<br><br>For example:<ul><li>If <code>offset</code> is 0 and <code>limit</code> is 10, the method will retrieve items 1-10 from the list of items returned</li><li>If <code>offset</code> is 10 and <code>limit</code> is 10, the method will retrieve items 11-20 from the list of items returned.</li></ul><span class="tablenote"><b>Note:</b> The value of <code>offset</code> must be either zero or a multiple of the value set in the <code>limit</code> field. An error is returned for invalid <code>offset</code> values.</span><br><span class="tablenote"><b>Note:</b> This method can return a maximum of 10,000 items in one results set.</span><br><b>Min</b>: 0<br><br><b>Max:</b> 9,999<br><br><b>Default:</b> 0</param>
        /// <param name="sort"><span class="tablenote"><b>Note:</b> This call currently returns results in a best-match order. This query parameter presently has no practical use.</span> For implementation help, refer to eBay API documentation at https://developer.ebay.com/api-docs/buy/browse/types/cos:SortField</param>
        /// <param name="requestBody">The container for the image information fields.</param>
        /// <returns>OK</returns>
        public async Task<SearchPagedCollection> SearchByImageAsync(string aspect_filter, string category_ids, string charity_ids, string fieldgroups, string filter, string limit, string offset, string sort, SearchByImageRequest requestBody, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
        {
            var requestUri = "item_summary/search_by_image?aspect_filter=" + (aspect_filter == null ? "" : System.Uri.EscapeDataString(aspect_filter)) + "&category_ids=" + (category_ids == null ? "" : System.Uri.EscapeDataString(category_ids)) + "&charity_ids=" + (charity_ids == null ? "" : System.Uri.EscapeDataString(charity_ids)) + "&fieldgroups=" + (fieldgroups == null ? "" : System.Uri.EscapeDataString(fieldgroups)) + "&filter=" + (filter == null ? "" : System.Uri.EscapeDataString(filter)) + "&limit=" + (limit == null ? "" : System.Uri.EscapeDataString(limit)) + "&offset=" + (offset == null ? "" : System.Uri.EscapeDataString(offset)) + "&sort=" + (sort == null ? "" : System.Uri.EscapeDataString(sort));
            using var httpRequestMessage = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Post, requestUri);
            using var requestWriter = new System.IO.StringWriter();
            var requestSerializer = JsonSerializer.Create(jsonSerializerSettings);
            requestSerializer.Serialize(requestWriter, requestBody);
            var content = new System.Net.Http.StringContent(requestWriter.ToString(), System.Text.Encoding.UTF8, "application/json");
            httpRequestMessage.Content = content;
            if (handleHeaders != null)
            {
                handleHeaders(httpRequestMessage.Headers);
            }

            var responseMessage = await httpClient.SendAsync(httpRequestMessage);
            try
            {
                responseMessage.EnsureSuccessStatusCodeEx();
                var streamContent = await responseMessage.Content.ReadAsStreamAsync();
                using JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(streamContent));
                var serializer = JsonSerializer.Create(jsonSerializerSettings);
                return serializer.Deserialize<SearchPagedCollection>(jsonReader);
            }
            finally
            {
                responseMessage.Dispose();
            }
        }

        /// <summary>
        /// This method retrieves the details of a specific item, such as description, price, category, all item aspects, condition, return policies, seller feedback and score, shipping options, shipping costs, estimated delivery, and other information the buyer needs to make a purchasing decision.<br><br>The Buy APIs are designed to let you create an eBay shopping experience in your app or website. This means you will need to know when something, such as the availability, quantity, etc., has changed in any eBay item you are offering. This is easily achieved by setting the <b>fieldgroups</b> URI parameter to one of the following values:<ul><li><code>COMPACT</code><br>Reduces the response to only those fields necessary in order to determine if any item detail has changed. This field group <b>must</b> be used alone.</li><li><code>PRODUCT</code><br>Adds fields to the default response that return information about the product/item. This field group may also be used in conjunction with <code>ADDITIONAL_SELLER_DETAILS</code>.</li><li><code>ADDITIONAL_SELLER_DETAILS</code><br>Adds an additional field to the response that returns the seller's user ID. This field group may also be used in conjunction with <code>PRODUCT</code>.</li></ul>For additional information, refer to <a href="/api-docs/buy/browse/resources/item/methods/getItem#uri.fieldgroups">fieldgroups</a>.<h3>Restrictions</h3>For a list of supported sites and other restrictions, refer to <a href="/api-docs/buy/browse/overview.html#API">API Restrictions</a>.<br><br><span class="tablenote"><b>eBay Partner Network:</b> In order to be commissioned for your sales, you must use the URL returned in the <code>itemAffiliateWebUrl</code> field to forward your buyer to the ebay.com site.</span>
        /// GetItem item/{item_id}
        /// </summary>
        /// <param name="fieldgroups">This parameter controls what is returned in the response. If this field is not set, the method returns all the details of the item.<br><br><span class="tablenote"><b>Note:</b> Multiple <b>fieldgroups</b> can be set and applied simultaneously. However, <b>COMPACT</b> <b>must</b> be used alone. Otherwise, an error will occur.</span><br><b>Valid Values:</b><ul><li><b>PRODUCT</b><br>This field group adds the <a href="/api-docs/buy/browse/resources/item/methods/getItem#response.product">product</a> container to the response.</li><li><b>COMPACT</b></br>This field group returns only the following fields which let you quickly check if the availability or price of the item has changed, if the item has been revised by the seller, or if an item's top-rated plus status has changed for items you have stored.<ul><li><b>itemId</b></li><li><b>bidCount</b></li><li><b>currentBidPrice</b></li><li><b>eligibleForInlineCheckout</b></li><li><b>estimatedAvailabilities</b></li><li><b>gtin</b></li><li><b>immediatePay</b></li><li><b>itemAffiliateWebURL</b></li><li><b>itemCreationDate</b></li><li><b>itemWebUrl</b></li><li><b>legacyItemId</b></li><li><b>minimumPriceToBid</b></li><li><b>price</b></li><li><b>priorityListing</b></li><li><b>reservePriceMet</b></li><li><b>sellerItemRevision</b></li><li><b>shippingOptions</b></li><li><b>taxes</b></li><li><b>topRatedBuyingExperience</b></li><li><b>uniqueBidderCount</b></li></ul>For Example:<br>To determine if a stored item's information is current, perform the following:<ol><li>Pass in the item ID and set <code>fieldgroups</code> to <code>COMPACT</code>.<pre>item/v1|4**********8|0?fieldgroups=COMPACT</pre></li><li>Do one of the following:<ul><li>If <b>sellerItemRevision</b> is returned and a revision number for this item <i>has not</i> been stored, record the number and pass in the item ID in the <b>getItem</b> method to retrieve the most recent information.</li><li>If the revision number is different from the value you have stored, update the value and pass in the item ID in the <b>getItem</b> method to retrieve the most recent information.</li><li>If <code>sellerItemRevision</code> is <i>not</i> returned or has not changed, where needed, update the item information with the information returned in the response.</li></ul></li></ol></li><li><b>ADDITIONAL_SELLER_DETAILS</b><br>This adds the <a href="/api-docs/buy/browse/resources/item/methods/getItem#response.seller.userId">userId</a> field to the response.</li></ul></param>
        /// <param name="item_id">This path parameter specifies the unique RESTful identifier of the item being retrieved. <br><br><b>RESTful Item ID Format: </b><code>v1</code>|<code><i>#</i></code>|<code><i>#</i></code><br><br>For a single SKU listing, pass in the item ID: <pre>v1|2**********2|0</pre>For a multi-SKU listing, pass in the identifier of the variation:<pre>v1|1**********2|4**********2</pre><br>For more information about item IDs for RESTful APIs, refer to <a href="/api-docs/buy/static/api-browse.html#Legacy" target="_blank">Item ID legacy API compatibility overview</a> in the <a href="/api-docs/buy/static/buying-ig-landing.html" target="_blank">Buying Integration Guide</a>.</param>
        /// <param name="quantity_for_shipping_estimate">This query parameter sets the item quantity to be used when calculating the shipping estimate information returned in the <a href="/api-docs/buy/browse/resources/item/methods/getItem#response.shippingOptions">shippingOptions</a> container of the response.<br><br>This value must be a positive integer value and should not exceed the quantity available in the listing. This field is not recommended for auction listings, as they will always have a quantity of 1.</param>
        /// <returns>Success</returns>
        public async Task<Item> GetItemAsync(string fieldgroups, string item_id, string quantity_for_shipping_estimate, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
        {
            var requestUri = "item/" + (item_id == null ? "" : System.Uri.EscapeDataString(item_id)) + "?fieldgroups=" + (fieldgroups == null ? "" : System.Uri.EscapeDataString(fieldgroups)) + "&quantity_for_shipping_estimate=" + (quantity_for_shipping_estimate == null ? "" : System.Uri.EscapeDataString(quantity_for_shipping_estimate));
            using var httpRequestMessage = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Get, requestUri);
            if (handleHeaders != null)
            {
                handleHeaders(httpRequestMessage.Headers);
            }

            var responseMessage = await httpClient.SendAsync(httpRequestMessage);
            try
            {
                responseMessage.EnsureSuccessStatusCodeEx();
                var streamContent = await responseMessage.Content.ReadAsStreamAsync();
                using JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(streamContent));
                var serializer = JsonSerializer.Create(jsonSerializerSettings);
                return serializer.Deserialize<Item>(jsonReader);
            }
            finally
            {
                responseMessage.Dispose();
            }
        }

        public async Task<Item> GetItemAsyncWithoutException(string fieldgroups, string item_id, string quantity_for_shipping_estimate, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
        {
            var requestUri = "item/" + (item_id == null ? "" : Uri.EscapeDataString(item_id)) + "?fieldgroups=" + (fieldgroups == null ? "" : Uri.EscapeDataString(fieldgroups) + "&quantity_for_shipping_estimate=" + (quantity_for_shipping_estimate == null ? "" : System.Uri.EscapeDataString(quantity_for_shipping_estimate)));
            using (var httpRequestMessage = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Get, requestUri))
            {
                if (handleHeaders != null)
                {
                    handleHeaders(httpRequestMessage.Headers);
                }

                var responseMessage = await httpClient.SendAsync(httpRequestMessage);
                //try
                //{
                //     var stream = await responseMessage.Content.ReadAsStreamAsync();
                //    var stream = await responseMessage.Content.ReadAsStreamAsync();
                //    using (JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(stream)))
                //    {
                //        var serializer = new JsonSerializer();
                //        var item = serializer.Deserialize<Item>(jsonReader);
                //        return item;
                //    }
                //}
                var jsonString = await responseMessage.Content.ReadAsStringAsync();
                try
                {
                    var item = JsonConvert.DeserializeObject<Item>(jsonString);
                    if (string.IsNullOrEmpty(item.Title))
                    {
                        return null;
                    }
                    return item;
                }
                catch (Exception ex)
                {

                }
                finally
                {
                    responseMessage.Dispose();
                }
            }

            return null;
        }
        public async Task<Item> GetItemAsyncWithoutExceptionOptimized(string fieldgroups, string item_id, string quantity_for_shipping_estimate, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
        {
            var requestUri = $"item/{Uri.EscapeDataString(item_id ?? "")}?fieldgroups={Uri.EscapeDataString(fieldgroups ?? "")}" + "&quantity_for_shipping_estimate=" + (quantity_for_shipping_estimate == null ? "" : System.Uri.EscapeDataString(quantity_for_shipping_estimate));
            using (var httpRequestMessage = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Get, requestUri))
            {
                handleHeaders?.Invoke(httpRequestMessage.Headers);

                using (var responseMessage = await httpClient.SendAsync(httpRequestMessage, System.Net.Http.HttpCompletionOption.ResponseHeadersRead))
                {
                    if (!responseMessage.IsSuccessStatusCode)
                    {
                        // Handle non-success status code as needed
                        return null;
                    }

                    try
                    {
                        using (var stream = await responseMessage.Content.ReadAsStreamAsync())
                        using (var streamReader = new System.IO.StreamReader(stream))
                        using (var jsonReader = new JsonTextReader(streamReader))
                        {
                            var serializer = new JsonSerializer();
                            var item = serializer.Deserialize<Item>(jsonReader);
                            return string.IsNullOrEmpty(item?.Title) ? null : item;
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception or handle as necessary
                        throw; // or return null based on your error handling policy
                    }
                }
            }
        }

        /// <summary>
        /// This method is a bridge between the eBay legacy APIs, such as <b>Shopping</b> and <b>Finding</b>, and the eBay Buy APIs. There are differences between how legacy APIs and RESTful APIs return the identifier of an "item" and what the item ID represents. This method lets you use the legacy item ids retrieve the details of a specific item, such as description, price, and other information the buyer needs to make a purchasing decision. It also returns the RESTful <b>item_id</b>, which you can use with all the Buy API  methods.<br><br>For additional information about how to use legacy ids with the Buy APIs, refer to <a href="/api-docs/buy/static/api-browse.html#Legacy" target="_blank">Item ID legacy API compatibility overview</a> in the Buying Integration guide.</br><br>This method returns the item details and requires you to pass in either the <b>item_id</b> of a non-variation item or the <b>item_id</b> values for both the parent and child of an item group.<br><br><span class="tablenote"><b>Note:</b> An item group is an item that has various aspect differences, such as color, size, storage capacity, etc.</span></br>When an item group is created, one of the item variations, such as the red shirt size L, is chosen as the "parent". All other items in the group are the children, such as the blue shirt size L, red shirt size M, etc.<br><br>The <b>fieldgroups</b> URI parameter lets you control what is returned in the response:<ul><li>Setting <b>fieldgroups</b> to <code>PRODUCT</code> adds additional fields to the default response that return information about the product of the item.</li><li>Setting the <b>fieldgroups</b> to <code>ADDITIONAL_SELLER_DETAILS</code> adds an additional field to the response that returns the seller's user ID.</li></ul>These <b>fieldgroups</b> can be used independently or at the same time. For additional information, refer to <a href="/api-docs/buy/browse/resources/item/methods/getItemByLegacyId#uri.fieldgroups">fieldgroups</a>.<h3>Restrictions</h3>For a list of supported sites and other restrictions, refer to <a href="/api-docs/buy/browse/overview.html#API">API Restrictions</a>.<br><br><span class="tablenote"><b>eBay Partner Network:</b> In order to be commissioned for your sales, you must use the URL returned in the <code>itemAffiliateWebUrl</code> field to forward your buyer to the ebay.com site.</span>
        /// GetItemByLegacyId item/get_item_by_legacy_id
        /// </summary>
        /// <param name="fieldgroups">This field controls what is returned in the response. If this field is not set, the method returns all details about the item. Multiple <code>fieldgroups</code> can be set.<br><br><b>Valid Values:</b><ul><li> <b>PRODUCT</b><br>This field group adds the <a href="/api-docs/buy/browse/resources/item/methods/getItemByLegacyId#response.product">product</a> container to the response. </li><li><b>ADDITIONAL_SELLER_DETAILS</b><br>This field group adds the <a href="/api-docs/buy/browse/resources/item/methods/getItemByLegacyId#response.seller.userId">userId</a> field to the response.</li></ul></param>
        /// <param name="legacy_item_id">This query parameter is the unique identifier that specifies the item being retrieved.<br><br><span class="tablenote"><b> Note:</b> When passing in the ID for a multi-variation listing, you must also use the <code>legacy_variation_id</code> field and pass in the ID of the specific item variation. If not, all variation within the multi-SKU listing will be retrieved.</span></li></ul><br>The following is an example of using the value of the <b>ItemID</b> field for a specific item to get the RESTful <code>itemId</code> value.<pre><code>browse/v1/item/get_item_by_legacy_id?legacy_item_id=1**********9</code></pre></param>
        /// <param name="legacy_variation_id">This query parameter specifies the legacy item ID of a specific item in a multi-variation listing, such as that for the <i>red shirt size L</i> item.<br><br><div class="msgbox_important"><p class="msgbox_importantInDiv" data-mc-autonum="&lt;b&gt;&lt;span style=&quot;color: #dd1e31;&quot; class=&quot;mcFormatColor&quot;&gt;Important! &lt;/span&gt;&lt;/b&gt;"><span class="autonumber"><span><b><span style="color: #dd1e31;" class="mcFormatColor">Important!</span></b></span></span> A <code>legacy_item_id</code> value must always be passed in when specifying a <code>legacy_variation_id</code> value.</p></div></param>
        /// <param name="legacy_variation_sku">This query parameter specifies the legacy SKU of an item. SKUs are the unique identifiers of an item created by the seller.<br><br>The following is an example of using the value of the <code>ItemID</code> and <code>SKU</code> fields to get the RESTful <code>itemId</code> value.<pre><code>browse/v1/item/get_item_by_legacy_id?legacy_item_id=1**********9&amp;legacy_variation_sku=V**********M</code></pre><div class="msgbox_important"><p class="msgbox_importantInDiv" data-mc-autonum="&lt;b&gt;&lt;span style=&quot;color: #dd1e31;&quot; class=&quot;mcFormatColor&quot;&gt;Important! &lt;/span&gt;&lt;/b&gt;"><span class="autonumber"><span><b><span style="color: #dd1e31;" class="mcFormatColor">Important!</span></b></span></span> A <code>legacy_item_id</code> value must always be passed in when specifying a <code>legacy_variation_sku</code> value.</p></div></param>
        /// <param name="quantity_for_shipping_estimate">This query parameter sets the item quantity to be used when calculating the shipping estimate information returned in the <a href="/api-docs/buy/browse/resources/item/methods/getItemByLegacyId#response.shippingOptions">shippingOptions</a> container of the response.<br><br>This value must be a positive integer value and should not exceed the quantity available in the listing. This field is not recommended for auction listings, as they will always have a quantity of 1.</param>
        /// <returns>OK</returns>
        public async Task<Item> GetItemByLegacyIdAsync(string fieldgroups, string legacy_item_id, string legacy_variation_id, string legacy_variation_sku, string quantity_for_shipping_estimate, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
        {
            var requestUri = "item/get_item_by_legacy_id?fieldgroups=" + (fieldgroups == null ? "" : System.Uri.EscapeDataString(fieldgroups)) + "&legacy_item_id=" + (legacy_item_id == null ? "" : System.Uri.EscapeDataString(legacy_item_id)) + "&legacy_variation_id=" + (legacy_variation_id == null ? "" : System.Uri.EscapeDataString(legacy_variation_id)) + "&legacy_variation_sku=" + (legacy_variation_sku == null ? "" : System.Uri.EscapeDataString(legacy_variation_sku)) + "&quantity_for_shipping_estimate=" + (quantity_for_shipping_estimate == null ? "" : System.Uri.EscapeDataString(quantity_for_shipping_estimate));
            using var httpRequestMessage = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Get, requestUri);
            if (handleHeaders != null)
            {
                handleHeaders(httpRequestMessage.Headers);
            }

            var responseMessage = await httpClient.SendAsync(httpRequestMessage);
            try
            {
                responseMessage.EnsureSuccessStatusCodeEx();
                var streamContent = await responseMessage.Content.ReadAsStreamAsync();
                using JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(streamContent));
                var serializer = JsonSerializer.Create(jsonSerializerSettings);
                return serializer.Deserialize<Item>(jsonReader);
            }
            finally
            {
                responseMessage.Dispose();
            }
        }

        /// <summary>
        /// This method retrieves the details about specific items that buyers need to make a purchasing decision.<br><br><span class="tablenote"><b>Note:</b> This is a <a href="/api-docs/static/versioning.html#limited " target="_blank"><img src="/cms/img/docs/partners-api.svg" class="legend-icon partners-icon" title="Limited Release"  alt="Limited Release" />(Limited Release)</a> available only to select Partners.<br><br>For this method, only the following fields are returned: <code>bidCount</code>, <code>currentBidPrice</code>, <code>eligibleForInlineCheckout</code>, <code>enabledForGuestCheckout</code>, <code>estimatedAvailabilities</code>, <code>gtin</code>, <code>immediatePay</code>, <code>itemAffiliateWebUrl</code>, <code>itemCreationDate</code>, <code>itemEndDate</code>, <code>itemId</code>, <code>itemWebUrl</code>, <code>legacyItemId</code>, <code>minimumPriceToBid</code>, <code>price</code>, <code>priorityListing</code>, <code>reservePriceMet</code>, <code>sellerItemRevision</code>, <code>taxes</code>, <code>topRatedBuyingExperience</code>, and <code>uniqueBidderCount</code>.<br><br>The array <code>shippingOptions</code>, which comprises multiple fields, is also returned if the <b>X-EBAY-C-ENDUSERCTX</b> header is supplied.</span><h3>Restrictions</h3>For a list of supported sites and other restrictions, refer to <a href="/api-docs/buy/browse/overview.html#API">API Restrictions</a>.<br><br><span class="tablenote"><b>eBay Partner Network:</b> In order to be commissioned for your sales, you must use the URL returned in the itemAffiliateWebUrl field to forward your buyer to the ebay.com site.
        /// GetItems item/
        /// </summary>
        /// <param name="item_ids">A comma separated list of the unique identifiers of the items to retrieve (maximum 20).<br><br><span class="tablenote"><b>Note:</b> In any given request, either <code>item_ids</code> <b>or</b> <code>item_group_ids</code> can be retrieved. Attempting to retrieve both will result in an error.</span><br><b>RESTful Item ID Format: </b><code>v1</code>|<code><i>#</i></code>|<code><i>#</i></code><br><br>For a single SKU listing, pass in the item ID: <pre>v1|2**********2|0</pre>For a multi-SKU listing, pass in the identifier of the variation:<pre>v1|1**********2|4**********2</pre><br>For more information about item IDs for RESTful APIs, refer to <a href="/api-docs/buy/static/api-browse.html#Legacy" target="_blank">Item ID legacy API compatibility overview</a> in the <a href="/api-docs/buy/static/buying-ig-landing.html" target="_blank">Buying Integration Guide</a>.</param>
        /// <param name="item_group_ids">A comma separated list of the unique identifiers of the item groups being retrieved (maximum 10).<br><br><span class="tablenote"><b>Note:</b> In any given request, either <code>item_ids</code> <b>or</b> <code>item_group_ids</code> can be retrieved. Attempting to retrieve both will result in an error.</span><br><b>RESTful Group Item ID Format:</b> <code>############</code><br><br>For example:<pre>3**********9</pre></param>
        /// <param name="quantity_for_shipping_estimate">This query parameter sets the item quantity to be used when calculating the shipping estimate information returned in the <a href="/api-docs/buy/browse/resources/item/methods/getItems#response.items.shippingOptions">shippingOptions</a> container of the response.<br><br>This value must be a positive integer value and should not exceed the quantity available in the listing. This field is not recommended for auction listings, as they will always have a quantity of 1.</param>
        /// <returns>Success</returns>
        public async Task<Items> GetItemsAsync(string item_ids, string item_group_ids, string quantity_for_shipping_estimate, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
        {
            var requestUri = "item/?item_ids=" + (item_ids == null ? "" : System.Uri.EscapeDataString(item_ids)) + "&item_group_ids=" + (item_group_ids == null ? "" : System.Uri.EscapeDataString(item_group_ids)) + "&quantity_for_shipping_estimate=" + (quantity_for_shipping_estimate == null ? "" : System.Uri.EscapeDataString(quantity_for_shipping_estimate));
            using var httpRequestMessage = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Get, requestUri);
            if (handleHeaders != null)
            {
                handleHeaders(httpRequestMessage.Headers);
            }

            var responseMessage = await httpClient.SendAsync(httpRequestMessage);
            try
            {
                responseMessage.EnsureSuccessStatusCodeEx();
                var streamContent = await responseMessage.Content.ReadAsStreamAsync();
                using JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(streamContent));
                var serializer = JsonSerializer.Create(jsonSerializerSettings);
                return serializer.Deserialize<Items>(jsonReader);
            }
            finally
            {
                responseMessage.Dispose();
            }
        }

        public async Task<Items> GetItemsAsyncWithoutException(string item_ids, string item_group_ids, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
        {
            var requestUri = "item/?item_ids=" + (item_ids == null ? "" : Uri.EscapeDataString(item_ids)) + "&item_group_ids=" + (item_group_ids == null ? "" : Uri.EscapeDataString(item_group_ids));
            using (var httpRequestMessage = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Get, requestUri))
            {
                if (handleHeaders != null)
                {
                    handleHeaders(httpRequestMessage.Headers);
                }

                var responseMessage = await httpClient.SendAsync(httpRequestMessage);
                try
                {
                    var streamContent = await responseMessage.Content.ReadAsStreamAsync();
                    using JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(streamContent));
                    var serializer = JsonSerializer.Create(jsonSerializerSettings);
                    return serializer.Deserialize<Items>(jsonReader);
                }
                finally
                {
                    responseMessage.Dispose();
                }
            }
        }

        /// <summary>
        /// This method retrieves details about individual items in an item group. An item group is an item that has various aspect differences, such as color, size, storage capacity, etc.<br><br>You pass in the <b>item_group_id</b> as a URI parameter.<br><br>This method returns two main containers:<ul><li><b>items</b><br>This container has an array of containers with the details about each item in the group.</li><li><b>commonDescriptions</b><br>This container has an array of containers for a description and the <code>item_ids</code> for all items that have this exact description. Because items within an item group often have the same description, this decreases the size of the response.</li></ul>Setting the <b>fieldgroup</b> to <code>ADDITIONAL_SELLER_DETAILS</code> adds an additional field to the response that returns the seller's user ID. For more information, refer to <a href="/api-docs/buy/browse/resources/item/methods/getItemsByItemGroup#uri.fieldgroups">fieldgroups</a>.<h3>Restrictions</h3>For a list of supported sites and other restrictions, refer to <a href="/api-docs/buy/browse/overview.html#API">API Restrictions</a>.<br><br><span class="tablenote"><b>eBay Partner Network: </b> In order to be commissioned for your sales, you must use the URL returned in the <code>itemAffiliateWebUrl</code> field to forward your buyer to the ebay.com site.</span>
        /// GetItemsByItemGroup item/get_items_by_item_group
        /// </summary>
        /// <param name="fieldgroups">This field controls what is returned in the response. If this field is not set, the method returns all details about the item.<br><br><b>Valid Values:</b><br><br><b>ADDITIONAL_SELLER_DETAILS</b> - This field group adds the <a href="/api-docs/buy/browse/resources/item/methods/getItemsByItemGroup#response.items.seller.userId">userId</a> field to the response.</param>
        /// <param name="item_group_id">This query parameter specifies the unique identifier of an item group for which information is to be returned. An item group is an item that has various aspect differences, such as color, size, storage capacity, etc.<br><br>This ID is returned in the <b>itemGroupHref</b> field of the <a href="/api-docs/buy/browse/resources/item_summary/methods/search">search</a> and <a href="/api-docs/buy/browse/resources/item/methods/getItem">getItem</a> methods.<br><br><b>For Example:</b><pre>https://api.ebay.com/buy/browse/v1/item/get_items_by_item_group?item_group_id=3**********6</pre></param>
        /// <param name="quantity_for_shipping_estimate">This query parameter sets the item quantity to be used when calculating the shipping estimate information returned in the <a href="/api-docs/buy/browse/resources/item/methods/getItemsByItemGroup#response.items.shippingOptions">shippingOptions</a> container of the response.<br><br>This value must be a positive integer value and should not exceed the quantity available in the listing. This field is not recommended for auction listings, as they will always have a quantity of 1.</param>
        /// <returns>OK</returns>
        public async Task<ItemGroup> GetItemsByItemGroupAsync(string fieldgroups, string item_group_id, string quantity_for_shipping_estimate, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
        {
            var requestUri = "item/get_items_by_item_group?fieldgroups=" + (fieldgroups == null ? "" : System.Uri.EscapeDataString(fieldgroups)) + "&item_group_id=" + (item_group_id == null ? "" : System.Uri.EscapeDataString(item_group_id)) + "&quantity_for_shipping_estimate=" + (quantity_for_shipping_estimate == null ? "" : System.Uri.EscapeDataString(quantity_for_shipping_estimate));
            using var httpRequestMessage = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Get, requestUri);
            if (handleHeaders != null)
            {
                handleHeaders(httpRequestMessage.Headers);
            }

            var responseMessage = await httpClient.SendAsync(httpRequestMessage);
            try
            {
                responseMessage.EnsureSuccessStatusCodeEx();
                var streamContent = await responseMessage.Content.ReadAsStreamAsync();
                using JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(streamContent));
                var serializer = JsonSerializer.Create(jsonSerializerSettings);
                return serializer.Deserialize<ItemGroup>(jsonReader);
            }
            finally
            {
                responseMessage.Dispose();
            }
        }

        /// <summary>
        /// This method checks if a product is compatible with the specified item. You can use this method to check the compatibility of cars, trucks, and motorcycles with a specific part listed on eBay.<br><br>For example, to check the compatibility of a part, you pass in the <b>item_id</b> of the part as a URI parameter and specify all the attributes used to define a specific car within the <b>compatibilityProperties</b> container. If the call is successful, the response will be <code>COMPATIBLE</code>, <code>NOT_COMPATIBLE</code>, or <code>UNDETERMINED</code>. Refer to <a href="/api-docs/buy/browse/resources/item/methods/checkCompatibility#response.compatibilityStatus">compatibilityStatus</a> for details.   <br><br><span class="tablenote"><b>Note:</b> The only products supported are cars, trucks, and motorcycles.</span><br>To find the attributes and values for a specific marketplace, you can use the compatibility methods in the <a href="/api-docs/commerce/taxonomy/resources/methods" target="_blank">Taxonomy API</a>. You can use this data to create menus to help buyers specify the product, such as their car.<br><br>For more information and a list of required attributes for the US marketplace that describe motor vehicles, refer to <a href="/api-docs/buy/static/api-browse.html#Check" target="_blank">Check compatibility</a> in the <i>Buying Integration Guide</i>.<br><br>For an example, refer to the <a href="/api-docs/buy/browse/resources/item/methods/checkCompatibility#h2-samples">Samples</a> section.<br><br><span class="tablenote"><b>Note:</b> This method is supported in Sandbox but <i>only</i> when passing in the specified <code>item_id</code> and compatibility name-value pairs listed in <a href="/api-docs/buy/browse/resources/item/methods/checkCompatibility#s0-1-22-6-7-7-6-SandboxSample-1">Sample 2: Sandbox Sample</a>.</span><h3>Restrictions</h3>For a list of supported sites and other restrictions, refer to <a href="/api-docs/buy/browse/overview.html#API">API Restrictions</a>.
        /// CheckCompatibility item/{item_id}/check_compatibility
        /// </summary>
        /// <param name="item_id">This path parameter specifies the unique RESTful identifier of an item (such as the park you want to check).<br><br><b>RESTful Item ID Format: </b><code>v1</code>|<code><i>#</i></code>|<code><i>#</i></code><br><br>For a single SKU listing, pass in the item ID: <pre>v1|2**********2|0</pre>For a multi-SKU listing, pass in the identifier of the variation:<pre>v1|1**********2|4**********2</pre><br>For more information about item IDs for RESTful APIs, refer to <a href="/api-docs/buy/static/api-browse.html#Legacy" target="_blank">Item ID legacy API compatibility overview</a> in the <a href="/api-docs/buy/static/buying-ig-landing.html" target="_blank">Buying Integration Guide</a>.</param>
        /// <returns>OK</returns>
        public async Task<CompatibilityResponse> CheckCompatibilityAsync(string item_id, CompatibilityPayload requestBody, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
        {
            var requestUri = "item/" + (item_id == null ? "" : System.Uri.EscapeDataString(item_id)) + "/check_compatibility";
            using var httpRequestMessage = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Post, requestUri);
            using var requestWriter = new System.IO.StringWriter();
            var requestSerializer = JsonSerializer.Create(jsonSerializerSettings);
            requestSerializer.Serialize(requestWriter, requestBody);
            var content = new System.Net.Http.StringContent(requestWriter.ToString(), System.Text.Encoding.UTF8, "application/json");
            httpRequestMessage.Content = content;
            if (handleHeaders != null)
            {
                handleHeaders(httpRequestMessage.Headers);
            }

            var responseMessage = await httpClient.SendAsync(httpRequestMessage);
            try
            {
                responseMessage.EnsureSuccessStatusCodeEx();
                var streamContent = await responseMessage.Content.ReadAsStreamAsync();
                using JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(streamContent));
                var serializer = JsonSerializer.Create(jsonSerializerSettings);
                return serializer.Deserialize<CompatibilityResponse>(jsonReader);
            }
            finally
            {
                responseMessage.Dispose();
            }
        }
    }
}

namespace Fonlow.Net.Http
{
    using System.Net.Http;

    public class WebApiRequestException : HttpRequestException
    {
        public new System.Net.HttpStatusCode StatusCode { get; private set; }

        public string Response { get; private set; }

        public System.Net.Http.Headers.HttpResponseHeaders Headers { get; private set; }

        public System.Net.Http.Headers.MediaTypeHeaderValue ContentType { get; private set; }

        public WebApiRequestException(string message, System.Net.HttpStatusCode statusCode, string response, System.Net.Http.Headers.HttpResponseHeaders headers, System.Net.Http.Headers.MediaTypeHeaderValue contentType) : base(message)
        {
            StatusCode = statusCode;
            Response = response;
            Headers = headers;
            ContentType = contentType;
        }
    }

    public static class ResponseMessageExtensions
    {
        public static void EnsureSuccessStatusCodeEx(this HttpResponseMessage responseMessage)
        {
            if (!responseMessage.IsSuccessStatusCode)
            {
                var responseText = responseMessage.Content.ReadAsStringAsync().Result;
                var contentType = responseMessage.Content.Headers.ContentType;
                throw new WebApiRequestException(responseMessage.ReasonPhrase, responseMessage.StatusCode, responseText, responseMessage.Headers, contentType);
            }
        }
    }
}
